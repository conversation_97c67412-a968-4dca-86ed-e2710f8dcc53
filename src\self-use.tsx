import { Log } from "./lib/common/log";
import { loadStorage, uiAddMoveStorageButtons } from "./lib/inventory";

const CONFIGS: Record<string, Array<() => void>> = {
  // outpost
  "index\\.php$": [removeOutpostButtonDelay],
  // yard
  "index\\.php\\?page=24": [loadStorage, uiAddMoveStorageButtons],
  // outpost inventory
  "index\\.php\\?page=25": [
    loadStorage,
    uiAddMoveStorageButtons,
    augmentInfoCardWithPriceInfo,
  ],
  // innercity inventory
  "DF3D_InventoryPage\\.php\\?page=31": [],
  // marketplace
  "index\\.php\\?page=35": [loadStorage, uiAddMoveStorageButtons],
};

unsafeWindow.addEventListener("load", () => {
  let path = unsafeWindow.location.href.split("/").pop()!;
  Log.info("applying config for", path);

  for (const regex in CONFIGS) {
    if (path.match(regex)) {
      Log.info("rule matched", regex);
      const config = CONFIGS[regex];
      config.forEach((f) => {
        Log.info("applying config", f.name);
        f();
      });
    }
  }

  Log.info("config applied");
});
