import { h } from "./lib/common/h";
import { fetchMarketData } from "./lib/market";
import { Item } from "./lib/common/item";

const DELAY_SEARCH_ON_HOVER = 500; // 500ms
export function augmentInfoCardWithPriceInfo() {
  const inventoryHolder = document.getElementById("inventoryholder");
  const infoBox = document.getElementById("infoBox");

  let currentTaskId = 0;
  let timeout = null;

  inventoryHolder.addEventListener("mousemove", (e) => {
    if (!(e.target instanceof HTMLElement)) {
      return;
    }

    // if the info card is not visible, clear states
    if (infoBox.style.visibility !== "visible") {
      if (timeout) {
        Log.debug(`clearing timeout ${timeout}`);
        clearTimeout(timeout);
        timeout = null;
        currentTaskId++;
      }
      return;
    }

    // if we have a pending task for this item, don't do anything
    if (timeout !== null) {
      return;
    }

    const executionTaskId = currentTaskId;
    timeout = setTimeout(async () => {
      // some time has already elapsed, need to check if we're still interested in this task
      if (executionTaskId !== currentTaskId) {
        return;
      }

      // show loading message and fetch data
      infoBox.appendChild(<br></br>);
      let priceInfoElement: HTMLDivElement = (
        <div
          className="itemData"
          style={{ fontStyle: "italic", textAlign: "left" }}
        >
          Loading price information...
        </div>
      );
      infoBox.appendChild(priceInfoElement);

      const result = await fetchMarketData(
        Item.fromElement(e.target as HTMLElement)
      );

      // check if we're still interested in this task
      if (executionTaskId !== currentTaskId) {
        return;
      }

      Log.debug("market data obtained", result);

      // display results
      priceInfoElement.textContent = "";
    }, DELAY_SEARCH_ON_HOVER);
  });
}
