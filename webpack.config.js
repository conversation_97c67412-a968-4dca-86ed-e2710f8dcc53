const path = require("path");
const webpack = require("webpack");

const entry = "./src/self-use.ts";

const banner = `// ==UserScript==
// @name         ZYScript
// @namespace    http://tampermonkey.net/
// @version      0.0.1
// @description  zys5945 selfuse
// <AUTHOR>
// @exclude      https://fairview.deadfrontier.com/onlinezombiemmo/index.php?action=login2
// @exclude      https://fairview.deadfrontier.com/onlinezombiemmo/index.php?action=logout*
// @match        https://fairview.deadfrontier.com/onlinezombiemmo/index.php*
// @match        https://fairview.deadfrontier.com/onlinezombiemmo/
// @grant        GM.getValue
// @grant        GM.setValue
// @grant        GM_xmlhttpRequest
// @grant        GM.xmlHttpRequest
// @grant        GM_openInTab
// @grant        GM.openInTab
// @license      GPL-3.0-or-later
// ==/UserScript==`;

module.exports = {
  mode: "production",
  optimization: {
    minimize: false,
  },
  entry,
  output: {
    filename: "out.js",
    path: path.resolve(__dirname, "dist"),
    clean: true,
    iife: false,
  },
  resolve: {
    extensions: [".ts", ".js"],
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: "ts-loader",
        exclude: /node_modules/,
      },
    ],
  },
  plugins: [
    new webpack.BannerPlugin({
      banner,
      raw: true,
      entryOnly: true,
    }),
  ],
};
