// interface OutpostData

interface LocationLink {
  // Display text or HTML content for the link
  content: string;
  page: number;
  mod?: number;
  // Indicates if the link is restricted for ironman accounts
  noIrons?: boolean;
  // Indicates if the link is restricted for hardcore accounts
  noHardcore?: boolean;
  // Optional user variable check required for access
  userVarCheck?: string;
  // Screen location of the link
  location: {
    top: number;
    left: number;
  };
}

interface OutpostData {
  links: Record<string, LocationLink>;
  background: string;
  logo: {
    image: string;
    location: string;
  };
}

declare var outpostData: OutpostData;
declare var outpostAttack: boolean;
declare var outpostLocation: string;
declare var arenaCount: number;

export default {
  abc: 10,
};
