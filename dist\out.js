// ==UserScript==
// @name         ZYScript
// @namespace    http://tampermonkey.net/
// @version      0.0.1
// @description  zys5945 selfuse
// <AUTHOR>
// @exclude      https://fairview.deadfrontier.com/onlinezombiemmo/index.php?action=login2
// @exclude      https://fairview.deadfrontier.com/onlinezombiemmo/index.php?action=logout*
// @match        https://fairview.deadfrontier.com/onlinezombiemmo/index.php*
// @match        https://fairview.deadfrontier.com/onlinezombiemmo/
// @grant        GM.getValue
// @grant        GM.setValue
// @grant        GM_xmlhttpRequest
// @grant        GM.xmlHttpRequest
// @grant        GM_openInTab
// @grant        GM.openInTab
// @license      GPL-3.0-or-later
// ==/UserScript==
/******/ "use strict";
/******/ var __webpack_modules__ = ({

/***/ 53:
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports["default"] = {
    abc: 10,
};


/***/ }),

/***/ 55:
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
const outpost_1 = __importDefault(__webpack_require__(53));
function overrideGlobal(name, f) {
    Object.defineProperty(window, name, {
        value: f(window[name]),
        writable: false,
        configurable: false,
    });
}
function overrideLoadSidebar() {
    overrideGlobal("loadSidebar", (loadSidebar) => {
        if (!loadSidebar) {
            return loadSidebar;
        }
        return (flshErl) => {
            const data = flshToArr(flshErl);
            if (!data) {
                console.warn(`loadSidebar called with ${flshErl} but flshToArr returned falsy`);
                return;
            }
            if (!userVars) {
                setUserVars(data);
            }
            else {
                updateIntoArr(data, userVars);
            }
            loadSidebar(flshErl);
        };
    });
}
function overrideNChangePage() {
    overrideGlobal("nChangePage", (_) => {
        return (e) => {
            var elem = e.currentTarget;
            if ((e.which !== 1 && e.which !== 2) || !elem) {
                return;
            }
            var nTab = false;
            if (e.which === 2) {
                nTab = true;
                e.preventDefault();
            }
            doPageChange(parseInt(elem.dataset.page), elem.dataset.mod, nTab);
        };
    });
}
const CONFIGS = {
    "": [overrideLoadSidebar, overrideNChangePage],
};
(() => {
    let path = new URL(window.location.href).pathname;
    path = path.slice(path.indexOf("index.php"), path.length);
    console.log("applying config for", path, outpost_1.default.abc);
    console.log(outpost_1.default.abc);
    const config = CONFIGS[path] ?? [];
    config.forEach((f) => f());
})();


/***/ })

/******/ });
/************************************************************************/
/******/ // The module cache
/******/ var __webpack_module_cache__ = {};
/******/ 
/******/ // The require function
/******/ function __webpack_require__(moduleId) {
/******/ 	// Check if module is in cache
/******/ 	var cachedModule = __webpack_module_cache__[moduleId];
/******/ 	if (cachedModule !== undefined) {
/******/ 		return cachedModule.exports;
/******/ 	}
/******/ 	// Create a new module (and put it into the cache)
/******/ 	var module = __webpack_module_cache__[moduleId] = {
/******/ 		// no module.id needed
/******/ 		// no module.loaded needed
/******/ 		exports: {}
/******/ 	};
/******/ 
/******/ 	// Execute the module function
/******/ 	__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 
/******/ 	// Return the exports of the module
/******/ 	return module.exports;
/******/ }
/******/ 
/************************************************************************/
/******/ 
/******/ // startup
/******/ // Load entry module and return exports
/******/ // This entry module is referenced by other modules so it can't be inlined
/******/ var __webpack_exports__ = __webpack_require__(55);
/******/ 
