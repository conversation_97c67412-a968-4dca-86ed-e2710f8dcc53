// ==UserScript==
// @name         ZYScript
// @namespace    http://tampermonkey.net/
// @version      0.0.1
// @description  General dead frontier 3D script with various quality of life improvements
// <AUTHOR>
// @exclude      https://fairview.deadfrontier.com/onlinezombiemmo/index.php?action=login2
// @exclude      https://fairview.deadfrontier.com/onlinezombiemmo/index.php?action=logout*
// @match        https://fairview.deadfrontier.com/onlinezombiemmo/index.php*
// @match        https://fairview.deadfrontier.com/onlinezombiemmo/
// @grant        GM.getValue
// @grant        GM.setValue
// @grant        GM_xmlhttpRequest
// @grant        GM.xmlHttpRequest
// @grant        GM_openInTab
// @grant        GM.openInTab
// @license      GPL-3.0-or-later
// ==/UserScript==
/******/ "use strict";
/******/ var __webpack_modules__ = ({

/***/ 55:
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
const common_1 = __webpack_require__(830);
const inventory_1 = __importDefault(__webpack_require__(227));
function removeOutpostButtonDelay() {
    const outpostLinkButtons = [];
    for (const elem of document.getElementsByClassName("opElem")) {
        if (elem.childElementCount === 1) {
            const maybeButton = elem.children[0];
            if (maybeButton.tagName === "BUTTON" &&
                maybeButton.dataset &&
                maybeButton.dataset.page) {
                outpostLinkButtons.push(maybeButton);
            }
        }
    }
    for (const button of outpostLinkButtons) {
        button.removeEventListener("mousedown", nChangePage);
        button.addEventListener("mousedown", (e) => {
            let elem = e.currentTarget;
            if (!elem || (e.which !== 1 && e.which !== 2)) {
                return;
            }
            let nTab = e.which === 2;
            if (nTab) {
                e.preventDefault();
            }
            doPageChange(parseInt(button.dataset.page), button.dataset.mod, false);
        });
    }
}
const CONFIGS = {
    "index\\.php$": [removeOutpostButtonDelay],
    "index\\.php\\?page=24": [
        inventory_1.default.loadStorage,
        inventory_1.default.uiAddMoveStorageButtons,
    ],
    "index\\.php\\?page=25": [
        inventory_1.default.loadStorage,
        inventory_1.default.uiAddMoveStorageButtons,
    ],
    "DF3D_InventoryPage\\.php\\?page=31": [],
    "index\\.php\\?page=35": [
        inventory_1.default.loadStorage,
        inventory_1.default.uiAddMoveStorageButtons,
    ],
};
unsafeWindow.addEventListener("load", () => {
    let path = unsafeWindow.location.href.split("/").pop();
    common_1.Log.info("applying config for", path);
    for (const regex in CONFIGS) {
        if (path.match(regex)) {
            common_1.Log.info("rule matched", regex);
            const config = CONFIGS[regex];
            config.forEach((f) => {
                common_1.Log.info("applying config", f.name);
                f();
            });
        }
    }
    common_1.Log.info("config applied");
});


/***/ }),

/***/ 227:
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
function loadStorage() {
    let dataArr = {
        pagetime: userVars["pagetime"],
        sc: userVars["sc"],
        userID: userVars["userID"],
        password: userVars["password"],
    };
    webCall("get_storage", dataArr, function (data) {
        unsafeWindow.storageBox = flshToArr(data);
    }, true);
}
function moveAllToStorage() {
    let dataArr = {
        pagetime: userVars["pagetime"],
        templateID: userVars["template_ID"],
        sc: userVars["sc"],
        gv: 21,
        userID: userVars["userID"],
        password: userVars["password"],
        action: "tostorage",
        slotnum: 1,
    };
    playSound("swap");
    promptLoading();
    webCall("hotrods/inventory_actions", dataArr, (data) => {
        promptEnd();
        updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
        populateInventory();
    }, true);
    reloadInventoryData();
}
function moveAllFromStorage() {
    let dataArr = {
        pagetime: userVars["pagetime"],
        templateID: userVars["template_ID"],
        sc: userVars["sc"],
        gv: 21,
        userID: userVars["userID"],
        password: userVars["password"],
        action: "fromstorage",
        slotnum: 1,
    };
    playSound("swap");
    promptLoading();
    webCall("hotrods/inventory_actions", dataArr, (data) => {
        promptEnd();
        updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
        populateInventory();
    }, true);
    reloadInventoryData();
}
function uiAddMoveStorageButtons() {
    const html = `<div style="position: absolute; right: 13px; bottom: 86px; z-index: 1;"><button id="storagetoinv" data-pmoverride=""><img src="/onlinezombiemmo/hotrods/hotrods_v${hrV}/HTML5/images/movein.png" width="40" data-amchild=""></button><button id="invtostorage" data-pmoverride=""><img src="/onlinezombiemmo/hotrods/hotrods_v${hrV}/HTML5/images/moveout.png" width="40" data-amchild=""></button></div>`;
    document
        .getElementById("inventoryholder")
        .insertAdjacentHTML("beforeend", html);
    document.getElementById("storagetoinv").addEventListener("click", () => {
        moveAllFromStorage();
    });
    document.getElementById("invtostorage").addEventListener("click", () => {
        moveAllToStorage();
    });
    const discardButton = document.querySelector('div.fakeSlot.hoverEffect[data-action="discard"]');
    discardButton.style.bottom = "150px";
    const discardLabel = [
        ...document.querySelectorAll("div.opElem"),
    ].find((el) => el.textContent.trim() === "Discard");
    discardLabel.style.bottom = "134px";
}
exports["default"] = {
    loadStorage,
    moveAllToStorage,
    moveAllFromStorage,
    uiAddMoveStorageButtons,
};


/***/ }),

/***/ 830:
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Log = void 0;
exports.overrideGlobal = overrideGlobal;
class Log {
    static format(...args) {
        return ["ZYScript:", ...args];
    }
    static error(...args) {
        console.error(...Log.format(...args));
    }
    static warn(...args) {
        console.warn(...Log.format(...args));
    }
    static info(...args) {
        console.info(...Log.format(...args));
    }
}
exports.Log = Log;
function overrideGlobal(name, f) {
    Object.defineProperty(unsafeWindow, name, {
        value: f(unsafeWindow[name]),
        writable: false,
        configurable: false,
    });
}


/***/ })

/******/ });
/************************************************************************/
/******/ // The module cache
/******/ var __webpack_module_cache__ = {};
/******/ 
/******/ // The require function
/******/ function __webpack_require__(moduleId) {
/******/ 	// Check if module is in cache
/******/ 	var cachedModule = __webpack_module_cache__[moduleId];
/******/ 	if (cachedModule !== undefined) {
/******/ 		return cachedModule.exports;
/******/ 	}
/******/ 	// Create a new module (and put it into the cache)
/******/ 	var module = __webpack_module_cache__[moduleId] = {
/******/ 		// no module.id needed
/******/ 		// no module.loaded needed
/******/ 		exports: {}
/******/ 	};
/******/ 
/******/ 	// Execute the module function
/******/ 	__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 
/******/ 	// Return the exports of the module
/******/ 	return module.exports;
/******/ }
/******/ 
/************************************************************************/
/******/ 
/******/ // startup
/******/ // Load entry module and return exports
/******/ // This entry module is referenced by other modules so it can't be inlined
/******/ var __webpack_exports__ = __webpack_require__(55);
/******/ 
