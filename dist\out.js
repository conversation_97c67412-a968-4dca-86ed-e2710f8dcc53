// ==UserScript==
// @name         ZYScript
// @namespace    http://tampermonkey.net/
// @version      0.0.1
// @description  General dead frontier 3D script with various quality of life improvements
// <AUTHOR>
// @exclude      https://fairview.deadfrontier.com/onlinezombiemmo/index.php?action=login2
// @exclude      https://fairview.deadfrontier.com/onlinezombiemmo/index.php?action=logout*
// @match        https://fairview.deadfrontier.com/onlinezombiemmo/index.php*
// @match        https://fairview.deadfrontier.com/onlinezombiemmo/
// @match        https://fairview.deadfrontier.com/onlinezombiemmo/DF3D/*
// @grant        GM.getValue
// @grant        GM.setValue
// @grant        GM_xmlhttpRequest
// @grant        GM.xmlHttpRequest
// @grant        GM_openInTab
// @grant        GM.openInTab
// @license      GPL-3.0-or-later
// ==/UserScript==
/******/ "use strict";
/******/ var __webpack_modules__ = ({

/***/ 227:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.loadStorage = loadStorage;
exports.moveAllToStorage = moveAllToStorage;
exports.moveAllFromStorage = moveAllFromStorage;
exports.uiAddMoveStorageButtons = uiAddMoveStorageButtons;
const webcall_1 = __webpack_require__(931);
function loadStorage() {
    let dataArr = {
        pagetime: userVars["pagetime"],
        sc: userVars["sc"],
        userID: userVars["userID"],
        password: userVars["password"],
    };
    webcall_1.WebCallManager.runWebCall("get_storage", dataArr, true).then((data) => {
        unsafeWindow.storageBox = flshToArr(data);
    });
}
function moveAllToStorage() {
    let dataArr = {
        pagetime: userVars["pagetime"],
        templateID: userVars["template_ID"],
        sc: userVars["sc"],
        gv: 21,
        userID: userVars["userID"],
        password: userVars["password"],
        action: "tostorage",
        slotnum: 1,
    };
    playSound("swap");
    promptLoading();
    webcall_1.WebCallManager.runWebCall("hotrods/inventory_actions", dataArr, true).then((data) => {
        promptEnd();
        updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
        populateInventory();
    });
}
function moveAllFromStorage() {
    let dataArr = {
        pagetime: userVars["pagetime"],
        templateID: userVars["template_ID"],
        sc: userVars["sc"],
        gv: 21,
        userID: userVars["userID"],
        password: userVars["password"],
        action: "fromstorage",
        slotnum: 1,
    };
    playSound("swap");
    promptLoading();
    webcall_1.WebCallManager.runWebCall("hotrods/inventory_actions", dataArr, true).then((data) => {
        promptEnd();
        updateIntoArr(flshToArr(data, "DFSTATS_"), userVars);
        populateInventory();
    });
}
function uiAddMoveStorageButtons() {
    const html = `<div style="position: absolute; right: 13px; bottom: 86px; z-index: 1;"><button id="storagetoinv" data-pmoverride=""><img src="/onlinezombiemmo/hotrods/hotrods_v${hrV}/HTML5/images/movein.png" width="40" data-amchild=""></button><button id="invtostorage" data-pmoverride=""><img src="/onlinezombiemmo/hotrods/hotrods_v${hrV}/HTML5/images/moveout.png" width="40" data-amchild=""></button></div>`;
    document
        .getElementById("inventoryholder")
        .insertAdjacentHTML("beforeend", html);
    document.getElementById("storagetoinv").addEventListener("click", () => {
        moveAllFromStorage();
    });
    document.getElementById("invtostorage").addEventListener("click", () => {
        moveAllToStorage();
    });
    const discardButton = document.querySelector('div.fakeSlot.hoverEffect[data-action="discard"]');
    discardButton.style.bottom = "150px";
    const discardLabel = [
        ...document.querySelectorAll("div.opElem"),
    ].find((el) => el.textContent.trim() === "Discard");
    discardLabel.style.bottom = "134px";
}


/***/ }),

/***/ 243:
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Log = void 0;
class Log {
    static format(...args) {
        return ["ZYScript:", ...args];
    }
    static error(...args) {
        console.error(...Log.format(...args));
    }
    static warn(...args) {
        console.warn(...Log.format(...args));
    }
    static info(...args) {
        console.info(...Log.format(...args));
    }
    static debug(...args) {
        console.debug(...Log.format(...args));
    }
}
exports.Log = Log;


/***/ }),

/***/ 655:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.fetchMarketData = fetchMarketData;
const item_1 = __webpack_require__(942);
const log_1 = __webpack_require__(243);
const webcall_1 = __webpack_require__(931);
const SERVICE_CATEGORIES = ["Doctor", "Chef", "Engineer"];
const CACHE_EXPIRATION = 5 * 1000;
const MARKET_CACHE = {};
async function fetchMarketData(searchCriteria) {
    if (searchCriteria instanceof item_1.Item) {
        searchCriteria = {
            searchname: searchCriteria.data["name"],
            category: searchCriteria.toMarketCategory(),
        };
    }
    if (!searchCriteria.searchname && !searchCriteria.category) {
        log_1.Log.error("fetchMarketData: no search criteria");
        return;
    }
    const cacheKey = JSON.stringify(searchCriteria);
    const cachedData = MARKET_CACHE[cacheKey];
    if (cachedData) {
        if (Date.now() - cachedData.timestamp < CACHE_EXPIRATION) {
            return cachedData.data;
        }
    }
    const dataArr = {
        pagetime: userVars["pagetime"],
        tradezone: searchCriteria.tradezone ?? userVars["DFSTATS_df_tradezone"],
        searchname: searchCriteria.searchname,
        memID: "",
    };
    if (SERVICE_CATEGORIES.includes(searchCriteria.category)) {
        dataArr["profession"] = searchCriteria.category;
        dataArr["category"] = "";
        dataArr["searchtype"] = "buyinglist";
        dataArr["search"] = "services";
    }
    else {
        dataArr["profession"] = "";
        dataArr["category"] = searchCriteria.category;
        if (searchCriteria.searchname) {
            dataArr["searchtype"] = "buyinglistitemname";
        }
        if (searchCriteria.category) {
            dataArr["searchtype"] = "buyinglistcategory";
        }
        if (searchCriteria.searchname && searchCriteria.category) {
            dataArr["searchtype"] = "buyinglistcategoryitemname";
        }
        dataArr["search"] = "trades";
    }
    const dataStr = await webcall_1.WebCallManager.runWebCall("trade_search", dataArr, true);
    const data = flshToArr(dataStr, "");
    MARKET_CACHE[cacheKey] = {
        data,
        timestamp: Date.now(),
    };
    return data;
}


/***/ }),

/***/ 785:
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.h = h;
function h(tag, props, ...children) {
    const el = document.createElement(tag);
    for (const [key, value] of Object.entries(props || {})) {
        if (key === "style" && value && typeof value === "object") {
            Object.assign(el.style, value);
        }
        else if (key.startsWith("on") && typeof value === "function") {
            el.addEventListener(key.substring(2).toLowerCase(), value);
        }
        else {
            el.setAttribute(key, value);
        }
    }
    for (const child of children.flat()) {
        el.appendChild(child instanceof Node ? child : document.createTextNode(child));
    }
    return el;
}


/***/ }),

/***/ 931:
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.WebCallManager = void 0;
exports.overrideGlobal = overrideGlobal;
exports.asyncWebCall = asyncWebCall;
const log_1 = __webpack_require__(243);
function overrideGlobal(name, f) {
    Object.defineProperty(unsafeWindow, name, {
        value: f(unsafeWindow[name]),
        writable: false,
        configurable: false,
    });
}
function asyncWebCall(call, params, hashed) {
    log_1.Log.debug("asyncWebCall", call, params, hashed);
    return new Promise((resolve, reject) => {
        try {
            webCall(call, params, (data) => {
                resolve(data);
            }, hashed);
        }
        catch (e) {
            reject(e);
        }
    });
}
class WebCallManager {
    static MAX_CONCURRENT_CALLS = 1;
    static RUNNING_TASKS = [];
    static TASK_QUEUE = [];
    static tick() {
        if (this.RUNNING_TASKS.length >= this.MAX_CONCURRENT_CALLS) {
            return;
        }
        const task = this.TASK_QUEUE.shift();
        if (!task) {
            return;
        }
        task.status = "running";
        this.RUNNING_TASKS.push(task);
        task.promise = task.taskFn();
        task.promise.finally(() => {
            task.status = "done";
            this.RUNNING_TASKS = this.RUNNING_TASKS.filter((t) => t !== task);
            this.tick();
        });
    }
    static queueTask(taskFn) {
        const task = {
            taskFn,
            promise: null,
            status: "pending",
        };
        this.TASK_QUEUE.push(task);
        this.tick();
        return task;
    }
    static spawnWebCall(call, params, hashed) {
        return this.queueTask(() => asyncWebCall(call, params, hashed));
    }
    static async runWebCall(call, params, hashed) {
        return await this.spawnWebCall(call, params, hashed).promise;
    }
}
exports.WebCallManager = WebCallManager;


/***/ }),

/***/ 942:
/***/ ((__unused_webpack_module, exports) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Item = void 0;
class Item {
    element;
    quality;
    quantity;
    type;
    itemtype;
    dataKey;
    details;
    data;
    fakeItem;
    constructor(element) {
        this.element = element;
        this.quality = parseInt(this.element.dataset["quality"]);
        this.quantity = parseInt(this.element.dataset["quantity"]);
        this.type = this.element.dataset["type"];
        const rawTypeArr = this.type.split("_");
        this.dataKey = rawTypeArr[0];
        this.details = rawTypeArr[1];
        this.data = globalData[this.dataKey];
        this.itemtype = this.data["itemtype"];
        this.fakeItem = this.element.classList.contains("fakeItem");
    }
    static fromElement(element) {
        while (element &&
            element.tagName !== "DIV" &&
            !element.classList.contains("fakeItem") &&
            !element.classList.contains("item")) {
            element = element.parentElement;
        }
        return new Item(element);
    }
    toMarketCategory() {
        if (this.itemtype === "weapon") {
            const weapon_type = this.data["type"];
            if (["submachinegun", "machinegun"].includes(weapon_type)) {
                return "weapon_lightmachinegun";
            }
            else if (["bigmachinegun", "minigun"].includes(weapon_type)) {
                return "weapon_heavymachinegun";
            }
            else if (weapon_type === "grenadelauncher") {
                return "weapon_grenadelauncher";
            }
            else {
                return "weapon_" + this.data["pro_type"];
            }
        }
        if (this.itemtype === "armour") {
            return "armour";
        }
        if (this.itemtype === "ammo") {
            if (this.type.indexOf("rifle") !== -1) {
                return "ammo_rifle";
            }
            else if (this.type.indexOf("gauge") !== -1) {
                return "ammo_shotgun";
            }
            else if (this.type.indexOf("grenade") !== -1) {
                return "ammo_grenade";
            }
            else if (this.type.indexOf("fuel") !== -1) {
                return "ammo_fuel";
            }
            else {
                return "ammo_handgun";
            }
        }
        if (this.itemtype === "item") {
            if (parseInt(this.data["foodrestore"]) > 0) {
                return "food";
            }
            if (parseInt(this.data["healthrestore"]) > 0) {
                return "medical";
            }
            if (this.data["clothingtype"]) {
                if (["mask", "hat"].includes(this.data["clothingtype"])) {
                    return "clothing_headwear";
                }
                else if (this.data["clothingtype"] === "coat") {
                    return "clothing_coat";
                }
                else {
                    return "clothing_basic";
                }
            }
            if (parseInt(this.data["barricade"]) === 1) {
                return "barricading";
            }
            if (this.data["implant"]) {
                return "implants";
            }
        }
        return "misc";
    }
}
exports.Item = Item;


/***/ })

/******/ });
/************************************************************************/
/******/ // The module cache
/******/ var __webpack_module_cache__ = {};
/******/ 
/******/ // The require function
/******/ function __webpack_require__(moduleId) {
/******/ 	// Check if module is in cache
/******/ 	var cachedModule = __webpack_module_cache__[moduleId];
/******/ 	if (cachedModule !== undefined) {
/******/ 		return cachedModule.exports;
/******/ 	}
/******/ 	// Create a new module (and put it into the cache)
/******/ 	var module = __webpack_module_cache__[moduleId] = {
/******/ 		// no module.id needed
/******/ 		// no module.loaded needed
/******/ 		exports: {}
/******/ 	};
/******/ 
/******/ 	// Execute the module function
/******/ 	__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 
/******/ 	// Return the exports of the module
/******/ 	return module.exports;
/******/ }
/******/ 
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it uses a non-standard name for the exports (exports).
(() => {
var exports = __webpack_exports__;
var __webpack_unused_export__;

__webpack_unused_export__ = ({ value: true });
const log_1 = __webpack_require__(243);
const inventory_1 = __webpack_require__(227);
const h_1 = __webpack_require__(785);
const market_1 = __webpack_require__(655);
const item_1 = __webpack_require__(942);
function removeOutpostButtonDelay() {
    const outpostLinkButtons = [];
    for (const elem of document.getElementsByClassName("opElem")) {
        if (elem.childElementCount === 1) {
            const maybeButton = elem.children[0];
            if (maybeButton.tagName === "BUTTON" &&
                maybeButton.dataset &&
                maybeButton.dataset.page) {
                outpostLinkButtons.push(maybeButton);
            }
        }
    }
    for (const button of outpostLinkButtons) {
        button.removeEventListener("mousedown", nChangePage);
        button.addEventListener("mousedown", (e) => {
            let elem = e.currentTarget;
            if (!elem || (e.which !== 1 && e.which !== 2)) {
                return;
            }
            let nTab = e.which === 2;
            if (nTab) {
                e.preventDefault();
            }
            doPageChange(parseInt(button.dataset.page), button.dataset.mod, false);
        });
    }
}
const DELAY_SEARCH_ON_HOVER = 500;
function augmentInfoCardWithPriceInfo() {
    const inventoryHolder = document.getElementById("inventoryholder");
    const infoBox = document.getElementById("infoBox");
    let currentTaskId = 0;
    let timeout = null;
    inventoryHolder.addEventListener("mousemove", (e) => {
        if (!(e.target instanceof HTMLElement)) {
            return;
        }
        if (infoBox.style.visibility !== "visible") {
            if (timeout) {
                log_1.Log.debug(`clearing timeout ${timeout}`);
                clearTimeout(timeout);
                timeout = null;
                currentTaskId++;
            }
            return;
        }
        if (timeout !== null) {
            return;
        }
        const executionTaskId = currentTaskId;
        timeout = setTimeout(async () => {
            if (executionTaskId !== currentTaskId) {
                return;
            }
            infoBox.appendChild((0, h_1.h)("br", null));
            let priceInfoElement = ((0, h_1.h)("div", { className: "itemData", style: { fontStyle: "italic", textAlign: "left" } }, "Loading price information..."));
            infoBox.appendChild(priceInfoElement);
            const result = await (0, market_1.fetchMarketData)(item_1.Item.fromElement(e.target));
            if (executionTaskId !== currentTaskId) {
                return;
            }
            log_1.Log.debug("market data obtained", result);
            priceInfoElement.textContent = "";
        }, DELAY_SEARCH_ON_HOVER);
    });
}
const CONFIGS = {
    "index\\.php$": [removeOutpostButtonDelay],
    "index\\.php\\?page=24": [inventory_1.loadStorage, inventory_1.uiAddMoveStorageButtons],
    "index\\.php\\?page=25": [
        inventory_1.loadStorage,
        inventory_1.uiAddMoveStorageButtons,
        augmentInfoCardWithPriceInfo,
    ],
    "DF3D_InventoryPage\\.php\\?page=31": [],
    "index\\.php\\?page=35": [inventory_1.loadStorage, inventory_1.uiAddMoveStorageButtons],
};
unsafeWindow.addEventListener("load", () => {
    let path = unsafeWindow.location.href.split("/").pop();
    log_1.Log.info("applying config for", path);
    for (const regex in CONFIGS) {
        if (path.match(regex)) {
            log_1.Log.info("rule matched", regex);
            const config = CONFIGS[regex];
            config.forEach((f) => {
                log_1.Log.info("applying config", f.name);
                f();
            });
        }
    }
    log_1.Log.info("config applied");
});

})();

