hoistPattern:
  - '*'
hoistedDependencies:
  '@discoveryjs/json-ext@0.6.3':
    '@discoveryjs/json-ext': private
  '@jridgewell/gen-mapping@0.3.13':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.11':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.30':
    '@jridgewell/trace-mapping': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/node@24.3.0':
    '@types/node': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@webpack-cli/configtest@3.0.1(webpack-cli@6.0.1)(webpack@5.101.3)':
    '@webpack-cli/configtest': private
  '@webpack-cli/info@3.0.1(webpack-cli@6.0.1)(webpack@5.101.3)':
    '@webpack-cli/info': private
  '@webpack-cli/serve@3.0.1(webpack-cli@6.0.1)(webpack@5.101.3)':
    '@webpack-cli/serve': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  acorn-import-phases@1.0.4(acorn@8.15.0):
    acorn-import-phases: private
  acorn@8.15.0:
    acorn: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@8.17.1:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.4:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  caniuse-lite@1.0.30001739:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  clone-deep@4.0.1:
    clone-deep: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  commander@12.1.0:
    commander: private
  cross-spawn@7.0.6:
    cross-spawn: private
  electron-to-chromium@1.5.211:
    electron-to-chromium: private
  enhanced-resolve@5.18.3:
    enhanced-resolve: private
  envinfo@7.14.0:
    envinfo: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  escalade@3.2.0:
    escalade: private
  eslint-scope@5.1.1:
    eslint-scope: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@4.3.0:
    estraverse: private
  events@3.3.0:
    events: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-uri@3.1.0:
    fast-uri: private
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: private
  fill-range@7.1.1:
    fill-range: private
  find-up@4.1.0:
    find-up: private
  flat@5.0.2:
    flat: private
  function-bind@1.1.2:
    function-bind: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-flag@4.0.0:
    has-flag: private
  hasown@2.0.2:
    hasown: private
  import-local@3.2.0:
    import-local: private
  interpret@3.1.1:
    interpret: private
  is-core-module@2.16.1:
    is-core-module: private
  is-number@7.0.0:
    is-number: private
  is-plain-object@2.0.4:
    is-plain-object: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  jest-worker@27.5.1:
    jest-worker: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  kind-of@6.0.3:
    kind-of: private
  loader-runner@4.3.0:
    loader-runner: private
  locate-path@5.0.0:
    locate-path: private
  merge-stream@2.0.0:
    merge-stream: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  neo-async@2.6.2:
    neo-async: private
  node-releases@2.0.19:
    node-releases: private
  p-limit@2.3.0:
    p-limit: private
  p-locate@4.1.0:
    p-locate: private
  p-try@2.2.0:
    p-try: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pkg-dir@4.2.0:
    pkg-dir: private
  randombytes@2.1.0:
    randombytes: private
  rechoir@0.8.0:
    rechoir: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve@1.22.10:
    resolve: private
  safe-buffer@5.2.1:
    safe-buffer: private
  schema-utils@4.3.2:
    schema-utils: private
  semver@7.7.2:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  shallow-clone@3.0.1:
    shallow-clone: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.7.6:
    source-map: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  tapable@2.2.3:
    tapable: private
  terser-webpack-plugin@5.3.14(webpack@5.101.3):
    terser-webpack-plugin: private
  terser@5.43.1:
    terser: private
  to-regex-range@5.0.1:
    to-regex-range: private
  undici-types@7.10.0:
    undici-types: private
  update-browserslist-db@1.1.3(browserslist@4.25.4):
    update-browserslist-db: private
  watchpack@2.4.4:
    watchpack: private
  webpack-merge@6.0.1:
    webpack-merge: private
  webpack-sources@3.3.3:
    webpack-sources: private
  which@2.0.2:
    which: private
  wildcard@2.0.1:
    wildcard: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Sat, 30 Aug 2025 20:14:59 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Desktop\df-scripts\node_modules\.pnpm
virtualStoreDirMaxLength: 60
