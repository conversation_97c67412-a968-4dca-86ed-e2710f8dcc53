function overrideGlobal(name, f) {
    Object.defineProperty(window, name, {
        value: f(window[name]),
        writable: false,
        configurable: false,
    });
}
function overrideLoadSidebar() {
    overrideGlobal("loadSidebar", (loadSidebar) => {
        if (!loadSidebar) {
            return loadSidebar;
        }
        return (flshErl) => {
            const data = flshToArr(flshErl);
            if (!data) {
                console.warn(`loadSidebar called with ${flshErl} but flshToArr returned falsy`);
                return;
            }
            if (!userVars) {
                setUserVars(data);
            }
            else {
                updateIntoArr(data, userVars);
            }
            loadSidebar(flshErl);
        };
    });
}
function overrideNChangePage() {
    overrideGlobal("nChangePage", (_) => {
        return (e) => {
            var elem = e.currentTarget;
            if ((e.which !== 1 && e.which !== 2) || !elem) {
                return;
            }
            var nTab = false;
            if (e.which === 2) {
                nTab = true;
                e.preventDefault();
            }
            doPageChange(parseInt(elem.dataset.page), elem.dataset.mod, nTab);
        };
    });
}
const CONFIGS = {
    "": [overrideLoadSidebar, overrideNChangePage],
};
(() => {
    let path = new URL(window.location.href).pathname;
    path = path.slice(path.indexOf("index.php"), path.length);
    console.log("applying config for", path, Outpost.abc);
    console.log(Outpost.abc);
    const config = CONFIGS[path] ?? [];
    config.forEach((f) => f());
})();
