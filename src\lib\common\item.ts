export class Item {
  element: HTMLDivElement;

  quality: number;
  quantity: number;
  // captures all important states
  type: string;
  // like category. can be missing from fake items
  itemtype: string;

  // the "type" attribute, or first part of the "type" attribute
  dataKey: string;
  // second part of the "type" attribute
  details: string | undefined;
  // retrieved data from globalData
  data: any;

  fakeItem: boolean;

  constructor(element: HTMLDivElement) {
    this.element = element;

    this.quality = parseInt(this.element.dataset["quality"]);
    this.quantity = parseInt(this.element.dataset["quantity"]);
    this.type = this.element.dataset["type"];

    const rawTypeArr = this.type.split("_");
    this.dataKey = rawTypeArr[0];
    this.details = rawTypeArr[1]; // TODO: may be more than 1
    this.data = globalData[this.dataKey];

    // even though dataset can have "itemtype" field, it's not accurate
    // e.g. for clothing its "itemtype" field on the dataset will actually have the value of "clothingtype" from globalData
    // this.itemtype = this.element.dataset["itemtype"];
    this.itemtype = this.data["itemtype"];

    this.fakeItem = this.element.classList.contains("fakeItem");
  }

  // search up the DOM tree for item element
  static fromElement(element: Element) {
    while (
      element &&
      element.tagName !== "DIV" &&
      !element.classList.contains("fakeItem") &&
      !element.classList.contains("item")
    ) {
      element = element.parentElement;
    }
    return new Item(element as HTMLDivElement);
  }

  /**
   * adapted from Dead Frontier - API by Shrike00
   */
  toMarketCategory() {
    if (this.itemtype === "weapon") {
      const weapon_type = this.data["type"];
      if (["submachinegun", "machinegun"].includes(weapon_type)) {
        return "weapon_lightmachinegun";
      } else if (["bigmachinegun", "minigun"].includes(weapon_type)) {
        return "weapon_heavymachinegun";
      } else if (weapon_type === "grenadelauncher") {
        return "weapon_grenadelauncher";
      } else {
        return "weapon_" + this.data["pro_type"];
      }
    }

    if (this.itemtype === "armour") {
      return "armour";
    }

    if (this.itemtype === "ammo") {
      if (this.type.indexOf("rifle") !== -1) {
        return "ammo_rifle";
      } else if (this.type.indexOf("gauge") !== -1) {
        return "ammo_shotgun";
      } else if (this.type.indexOf("grenade") !== -1) {
        return "ammo_grenade";
      } else if (this.type.indexOf("fuel") !== -1) {
        return "ammo_fuel";
      } else {
        return "ammo_handgun";
      }
    }

    if (this.itemtype === "item") {
      if (parseInt(this.data["foodrestore"]) > 0) {
        return "food";
      }
      if (parseInt(this.data["healthrestore"]) > 0) {
        return "medical";
      }
      if (this.data["clothingtype"]) {
        if (["mask", "hat"].includes(this.data["clothingtype"])) {
          return "clothing_headwear";
        } else if (this.data["clothingtype"] === "coat") {
          return "clothing_coat";
        } else {
          return "clothing_basic";
        }
      }
      if (parseInt(this.data["barricade"]) === 1) {
        return "barricading";
      }
      if (this.data["implant"]) {
        return "implants";
      }
    }

    return "misc";
  }
}
