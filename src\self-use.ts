import { Log } from "./lib/common";
import Inventory from "./lib/inventory";

function removeOutpostButtonDelay() {
  const outpostLinkButtons = [];
  for (const elem of document.getElementsByClassName("opElem")) {
    if (elem.childElementCount === 1) {
      const maybeButton = elem.children[0] as any;
      if (
        maybeButton.tagName === "BUTTON" &&
        maybeButton.dataset &&
        maybeButton.dataset.page
      ) {
        outpostLinkButtons.push(maybeButton);
      }
    }
  }

  for (const button of outpostLinkButtons) {
    button.removeEventListener("mousedown", nChangePage);
    button.addEventListener("mousedown", (e) => {
      let elem = e.currentTarget as any;
      if (!elem || (e.which !== 1 && e.which !== 2)) {
        return;
      }
      let nTab = e.which === 2;
      if (nTab) {
        e.preventDefault();
      }
      doPageChange(parseInt(button.dataset.page), button.dataset.mod, false);
    });
  }
}

function augmentInfoCardWithPriceInfo() {
  const inventoryHolder = document.getElementById("inventoryholder");
  const infoBox = document.getElementById("infoBox");

  inventoryHolder.addEventListener("mousemove", (e) => {
    if (infoBox.style.visibility !== "visible") {
      return;
    }

    Log.info("mousemove", e);
  });
}

// sidebar;
// inventoryholder;

const CONFIGS: Record<string, Array<() => void>> = {
  // outpost
  "index\\.php$": [removeOutpostButtonDelay],
  // yard
  "index\\.php\\?page=24": [
    Inventory.loadStorage,
    Inventory.uiAddMoveStorageButtons,
  ],
  // outpost inventory
  "index\\.php\\?page=25": [
    Inventory.loadStorage,
    Inventory.uiAddMoveStorageButtons,
  ],
  // innercity inventory
  "DF3D_InventoryPage\\.php\\?page=31": [],
  // marketplace
  "index\\.php\\?page=35": [
    Inventory.loadStorage,
    Inventory.uiAddMoveStorageButtons,
  ],
};

unsafeWindow.addEventListener("load", () => {
  let path = unsafeWindow.location.href.split("/").pop()!;
  Log.info("applying config for", path);

  for (const regex in CONFIGS) {
    if (path.match(regex)) {
      Log.info("rule matched", regex);
      const config = CONFIGS[regex];
      config.forEach((f) => {
        Log.info("applying config", f.name);
        f();
      });
    }
  }

  Log.info("config applied");
});
