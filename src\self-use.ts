// ==UserScript==
// @name         ZYScript
// @namespace    http://tampermonkey.net/
// @version      0.0.1
// @description  zys5945 self use
// <AUTHOR>
// @exclude      https://fairview.deadfrontier.com/onlinezombiemmo/index.php?action=login2
// @exclude      https://fairview.deadfrontier.com/onlinezombiemmo/index.php?action=logout*
// @match        https://fairview.deadfrontier.com/onlinezombiemmo/index.php*
// @match        https://fairview.deadfrontier.com/onlinezombiemmo/
// @grant        GM.getValue
// @grant        GM.setValue
// @grant        GM_xmlhttpRequest
// @grant        GM.xmlHttpRequest
// @grant        GM_openInTab
// @grant        GM.openInTab
// @license      GPL-3.0-or-later
// ==/UserScript==

import Outpost from "./lib/outpost";

/**
 * @param name name of window property to override
 * @param f function that produces the new value of the property based on the old value
 */
function overrideGlobal(name: string, f: (oldValue: any) => any) {
  Object.defineProperty(window, name, {
    value: f((window as any)[name]),
    writable: false,
    configurable: false,
  });
}

// override loadSidebar to also populate userVars
function overrideLoadSidebar() {
  overrideGlobal("loadSidebar", (loadSidebar) => {
    if (!loadSidebar) {
      return loadSidebar;
    }

    return (flshErl: string) => {
      const data = flshToArr(flshErl);
      if (!data) {
        console.warn(
          `loadSidebar called with ${flshErl} but flshToArr returned falsy`
        );
        return;
      }

      if (!userVars) {
        setUserVars(data);
      } else {
        updateIntoArr(data, userVars);
      }

      loadSidebar(flshErl);
    };
  });
}

function overrideNChangePage() {
  overrideGlobal("nChangePage", (_) => {
    return (e: MouseEvent) => {
      var elem = e.currentTarget as any;
      if ((e.which !== 1 && e.which !== 2) || !elem) {
        return;
      }

      var nTab = false;
      if (e.which === 2) {
        nTab = true;
        e.preventDefault();
      }

      doPageChange(parseInt(elem.dataset.page), elem.dataset.mod, nTab);
    };
  });
}

const CONFIGS: Record<string, Array<() => void>> = {
  // outpost page
  "": [overrideLoadSidebar, overrideNChangePage],
};

(() => {
  let path = new URL(window.location.href).pathname;
  path = path.slice(path.indexOf("index.php"), path.length);
  console.log("applying config for", path, Outpost.abc);
  console.log(Outpost.abc);
  const config = CONFIGS[path] ?? [];
  config.forEach((f) => f());
})();
