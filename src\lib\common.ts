export class Log {
  static format(...args: any[]) {
    return ["ZYScript:", ...args];
  }
  static error(...args: any[]) {
    console.error(...Log.format(...args));
  }
  static warn(...args: any[]) {
    console.warn(...Log.format(...args));
  }
  static info(...args: any[]) {
    console.info(...Log.format(...args));
  }
}

export interface ItemElement extends HTMLDivElement {
  dataset: {
    itemtype: string;
    quality: string;
    // for armor, this is the remaining durability
    quantity: string;
    /**
     * this is key into globalData
     * for weapons and armors, it is affixed by _stats<nums>
     */
    type: string;
  };
}

/**
 * @param name name of window property to override
 * @param f function that produces the new value of the property based on the old value
 */
export function overrideGlobal(name: string, f: (oldValue: any) => any) {
  Object.defineProperty(unsafeWindow, name, {
    value: f((unsafeWindow as any)[name]),
    writable: false,
    configurable: false,
  });
}

export class Item {
  dataset: ItemElement["dataset"];

  dataKey: string;
  data: any;

  stats: string | undefined;

  constructor(public element: ItemElement) {
    this.dataset = this.element.dataset;

    const rawTypeArr = this.element.dataset.type.split("_");
    this.dataKey = rawTypeArr[0];
    this.stats = rawTypeArr[1];

    this.data = globalData[this.dataKey];
  }

  /**
   * adapted from Dead Frontier - API by Shrike00
   */
  toMarketCategory() {
    if (this.dataset.itemtype === "weapon") {
      const weapon_type = this.data["type"];
      if (["submachinegun", "machinegun"].includes(weapon_type)) {
        return "weapon_lightmachinegun";
      } else if (["bigmachinegun", "minigun"].includes(weapon_type)) {
        return "weapon_heavymachinegun";
      } else if (weapon_type === "grenadelauncher") {
        return "weapon_grenadelauncher";
      } else {
        return "weapon_" + this.data["pro_type"];
      }
    }

    if (this.dataset.itemtype === "armour") {
      return "armour";
    }

    if (this.dataset.itemtype === "ammo") {
      if (this.dataset.type.indexOf("rifle") !== -1) {
        return "ammo_rifle";
      } else if (this.dataset.type.indexOf("gauge") !== -1) {
        return "ammo_shotgun";
      } else if (this.dataset.type.indexOf("grenade") !== -1) {
        return "ammo_grenade";
      } else if (this.dataset.type.indexOf("fuel") !== -1) {
        return "ammo_fuel";
      } else {
        return "ammo_handgun";
      }
    }

    if (this.dataset.itemtype === "item") {
      if (parseInt(globalData[this.dataset.type]["foodrestore"]) > 0) {
        return "food";
      }
      if (parseInt(globalData[this.dataset.type]["healthrestore"]) > 0) {
        return "medical";
      }
      if (this.data["clothingtype"]) {
        if (["mask", "hat"].includes(this.data["clothingtype"])) {
          return "clothing_headwear";
        } else if (this.data["clothingtype"] === "coat") {
          return "clothing_coat";
        } else {
          return "clothing_basic";
        }
      }
      if (parseInt(this.data["barricade"]) === 1) {
        return "barricading";
      }
      if (this.data["implant"]) {
        return "implants";
      }
    }

    return "misc";
  }
}
