import { Item } from "./common/item";
import { Log } from "./common/log";
import { WebCallManager } from "./common/webcall";

const SERVICE_CATEGORIES = ["Doctor", "Chef", "Engineer"];

const CACHE_EXPIRATION = 5 * 1000; // 5 seconds

interface MarketCacheEntry {
  data: any;
  timestamp: number;
}
const MARKET_CACHE: Record<string, MarketCacheEntry> = {};

export interface SearchCriteria {
  searchname?: string;
  category?: string;
  tradezone?: number | string;
}

/**
 * can be used in many places
 * data format:
 *  always present:
 *      done: "1"
 *      tradelist_maxresults: "<num>"
 *      then a list of result entries, index starts from 0 and ends with tradelist_maxresults - 1
 *
 *  item results
 *      tradelist_<index>_trade_id: "<id>"
 *      tradelist_<index>_id_member: "<id>"
 *      tradelist_<index>_member_name: "<name>"
 *      tradelist_<index>_id_member_to: "0"
 *      tradelist_<index>_member_to_name: ""
 *      tradelist_<index>_item: "<item>"
 *      tradelist_<index>_itemname: "<name>"
 *      tradelist_<index>_price: "<price>"
 *      tradelist_<index>_trade_zone: "<tradezone>"
 *      tradelist_<index>_category: "<category>"
 *      tradelist_<index>_quantity: "<quantity>"
 *      tradelist_<index>_priceper: "<priceper>"
 *      tradelist_<index>_deny_private: "0"
 *
 *  service results
 *      tradelist_<index>_available: "1"
 *      tradelist_<index>_id_member: "<id>"
 *      tradelist_<index>_level: "<level>"
 *      tradelist_<index>_member_name: "<name>"
 *      tradelist_<index>_price: "<price>"
 *      tradelist_<index>_profession: "<profession>"
 *      tradelist_<index>_trade_zone: "<tradezone>"
 *
 */
export async function fetchMarketData(
  searchCriteria: SearchCriteria | Item
): Promise<any> {
  if (searchCriteria instanceof Item) {
    searchCriteria = {
      searchname: searchCriteria.data["name"],
      category: searchCriteria.toMarketCategory(),
    };
  }

  if (!searchCriteria.searchname && !searchCriteria.category) {
    Log.error("fetchMarketData: no search criteria");
    return;
  }

  // lookup cache
  const cacheKey = JSON.stringify(searchCriteria);
  const cachedData = MARKET_CACHE[cacheKey];
  if (cachedData) {
    if (Date.now() - cachedData.timestamp < CACHE_EXPIRATION) {
      return cachedData.data;
    }
  }

  const dataArr = {
    pagetime: userVars["pagetime"],
    tradezone: searchCriteria.tradezone ?? userVars["DFSTATS_df_tradezone"],
    searchname: searchCriteria.searchname,
    memID: "",
  };

  if (SERVICE_CATEGORIES.includes(searchCriteria.category)) {
    dataArr["profession"] = searchCriteria.category;
    dataArr["category"] = "";
    dataArr["searchtype"] = "buyinglist";
    dataArr["search"] = "services";
  } else {
    dataArr["profession"] = "";
    dataArr["category"] = searchCriteria.category;

    if (searchCriteria.searchname) {
      dataArr["searchtype"] = "buyinglistitemname";
    }
    if (searchCriteria.category) {
      dataArr["searchtype"] = "buyinglistcategory";
    }
    if (searchCriteria.searchname && searchCriteria.category) {
      dataArr["searchtype"] = "buyinglistcategoryitemname";
    }
    dataArr["search"] = "trades";
  }

  const dataStr = await WebCallManager.runWebCall(
    "trade_search",
    dataArr,
    true
  );
  const data = flshToArr(dataStr, "") as any;
  MARKET_CACHE[cacheKey] = {
    data,
    timestamp: Date.now(),
  };
  return data;
}
