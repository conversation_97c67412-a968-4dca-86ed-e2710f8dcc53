{"sc": "ca30f254f9b412cad1c84fdd895fa866", "userID": "********", "template_ID": "0", "password": "02c5eb23839347bfed157e6277364004735bad2e", "connection": "dfflashconn4327", "DFSTATS_id_member": "********", "DFSTATS_id_character": "1", "DFSTATS_df_name": "zys5945", "DFSTATS_df_gender": "Male", "DFSTATS_df_rank": "Exterminator", "DFSTATS_df_profession": "Investigator", "DFSTATS_df_refid": "0", "DFSTATS_df_ahead": "3", "DFSTATS_df_abody": "5", "DFSTATS_df_alegs": "6", "DFSTATS_df_level": "123", "DFSTATS_df_maxachievedlevel": "0", "DFSTATS_df_freepoints": "0", "DFSTATS_df_dead": "0", "DFSTATS_df_dangerlevel": "0", "DFSTATS_df_cash": 111, "DFSTATS_df_bankcash": "8503357", "DFSTATS_df_credits": 0, "DFSTATS_df_buyer": "1", "DFSTATS_df_goldmember": "1", "DFSTATS_df_goldmembertime": "*********", "DFSTATS_df_exp": "5743116", "DFSTATS_df_exptotal": "*********", "DFSTATS_df_expstart": "*********", "DFSTATS_df_hpmax": "100", "DFSTATS_df_hpcurrent": "100", "DFSTATS_df_hungerhp": "71", "DFSTATS_df_armourstr": "75", "DFSTATS_df_armourhp": "0", "DFSTATS_df_armourhpmax": "300", "DFSTATS_df_armourname": "Exterminator Mesh XT", "DFSTATS_df_armourtype": "exterminatormeshxt_stats2424", "DFSTATS_df_weapon1type": "shortfusecrossbow_stats888", "DFSTATS_df_weapon1name": "Short Fuse Crossbow", "DFSTATS_df_weapon1ammo": "heavygrenadeammo", "DFSTATS_df_weapon2type": "goretooth44g_stats888", "DFSTATS_df_weapon2name": "Goretooth 44G", "DFSTATS_df_weapon2ammo": "", "DFSTATS_df_weapon3type": "battleaxe_stats888", "DFSTATS_df_weapon3name": "Battle Axe", "DFSTATS_df_weapon3ammo": "", "DFSTATS_df_strength": "50", "DFSTATS_df_accuracy": "80", "DFSTATS_df_agility": "100", "DFSTATS_df_endurance": "50", "DFSTATS_df_criticalhit": "88", "DFSTATS_df_reloading": "100", "DFSTATS_df_dexterity": "0", "DFSTATS_df_survival": 25, "DFSTATS_df_promelee": "135", "DFSTATS_df_propistol": "10", "DFSTATS_df_proshotgun": "0", "DFSTATS_df_promachinegun": "120", "DFSTATS_df_proexplosive": "21", "DFSTATS_df_prorifle": "120", "DFSTATS_df_positionx": "1054", "DFSTATS_df_positiony": "987", "DFSTATS_df_positionz": "0", "DFSTATS_df_invslots": "30", "DFSTATS_df_inv1_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DFSTATS_df_inv1_quantity": "1200", "DFSTATS_df_inv2_type": "", "DFSTATS_df_inv2_quantity": "0", "DFSTATS_df_inv3_type": "", "DFSTATS_df_inv3_quantity": "0", "DFSTATS_df_inv4_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DFSTATS_df_inv4_quantity": "605", "DFSTATS_df_inv5_type": "heavygrenadeammo", "DFSTATS_df_inv5_quantity": "57", "DFSTATS_df_inv6_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DFSTATS_df_inv6_quantity": "564", "DFSTATS_df_inv7_type": "", "DFSTATS_df_inv7_quantity": "0", "DFSTATS_df_inv8_type": "", "DFSTATS_df_inv8_quantity": "0", "DFSTATS_df_inv9_type": "", "DFSTATS_df_inv9_quantity": "0", "DFSTATS_df_inv10_type": "", "DFSTATS_df_inv10_quantity": "0", "DFSTATS_df_inv11_type": "", "DFSTATS_df_inv11_quantity": "0", "DFSTATS_df_inv12_type": "", "DFSTATS_df_inv12_quantity": "0", "DFSTATS_df_inv13_type": "", "DFSTATS_df_inv13_quantity": "0", "DFSTATS_df_inv14_type": "", "DFSTATS_df_inv14_quantity": "0", "DFSTATS_df_inv15_type": "", "DFSTATS_df_inv15_quantity": "0", "DFSTATS_df_inv16_type": "", "DFSTATS_df_inv16_quantity": "0", "DFSTATS_df_inv17_type": "", "DFSTATS_df_inv17_quantity": "0", "DFSTATS_df_inv18_type": "", "DFSTATS_df_inv18_quantity": "0", "DFSTATS_df_inv19_type": "", "DFSTATS_df_inv19_quantity": "0", "DFSTATS_df_inv20_type": "", "DFSTATS_df_inv20_quantity": "0", "DFSTATS_df_inv21_type": "", "DFSTATS_df_inv21_quantity": "0", "DFSTATS_df_inv22_type": "", "DFSTATS_df_inv22_quantity": "0", "DFSTATS_df_inv23_type": "", "DFSTATS_df_inv23_quantity": "0", "DFSTATS_df_inv24_type": "", "DFSTATS_df_inv24_quantity": "0", "DFSTATS_df_inv25_type": "", "DFSTATS_df_inv25_quantity": "0", "DFSTATS_df_inv26_type": "", "DFSTATS_df_inv26_quantity": "0", "DFSTATS_df_inv27_type": "", "DFSTATS_df_inv27_quantity": "0", "DFSTATS_df_inv28_type": "", "DFSTATS_df_inv28_quantity": "0", "DFSTATS_df_inv29_type": "10gaugeammo", "DFSTATS_df_inv29_quantity": "415", "DFSTATS_df_inv30_type": "energycellammo", "DFSTATS_df_inv30_quantity": "1119", "DFSTATS_df_hasservice": "0", "DFSTATS_df_tradezone": "21", "DFSTATS_df_32ammo": "0", "DFSTATS_df_35ammo": "0", "DFSTATS_df_357ammo": "0", "DFSTATS_df_38ammo": "0", "DFSTATS_df_40ammo": "0", "DFSTATS_df_45ammo": "0", "DFSTATS_df_50ammo": "0", "DFSTATS_df_55ammo": "0", "DFSTATS_df_55rifleammo": "0", "DFSTATS_df_75rifleammo": "0", "DFSTATS_df_9rifleammo": "0", "DFSTATS_df_127rifleammo": "564", "DFSTATS_df_14rifleammo": "1805", "DFSTATS_df_12gaugeammo": "0", "DFSTATS_df_16gaugeammo": "0", "DFSTATS_df_20gaugeammo": "0", "DFSTATS_df_10gaugeammo": "415", "DFSTATS_df_heavygrenadeammo": "457", "DFSTATS_df_grenadeammo": "0", "DFSTATS_df_fuelammo": "0", "DFSTATS_df_energycellammo": "1119", "DFSTATS_df_biomassammo": "0", "DFSTATS_df_previousposition1": "", "DFSTATS_df_previousposition2": "", "DFSTATS_df_previousposition3": "", "DFSTATS_df_previousposition4": "", "DFSTATS_df_previousposition5": "", "DFSTATS_df_previousposition6": "", "DFSTATS_df_itemgen1_type": "", "DFSTATS_df_itemgen1_quantity": "0", "DFSTATS_df_itemgen2_type": "", "DFSTATS_df_itemgen2_quantity": "0", "DFSTATS_df_itemgen3_type": "", "DFSTATS_df_itemgen3_quantity": "0", "DFSTATS_df_secretshop": "0", "DFSTATS_df_lastpvp1": "0", "DFSTATS_df_lastpvp2": "0", "DFSTATS_df_lastmostdamage1": "0", "DFSTATS_df_lastmostdamage2": "0", "DFSTATS_df_lasthitby": "0", "DFSTATS_df_mostdamageby": "0", "DFSTATS_df_defensetime": "0", "DFSTATS_df_defensearea": "", "DFSTATS_df_defensewins": "0", "DFSTATS_df_minioutpost": "1", "DFSTATS_df_minioutpostattack": "0", "DFSTATS_df_minioutpostname": "Se<PERSON>ron<PERSON> Bunker", "DFSTATS_df_minioutpostx": "1054", "DFSTATS_df_minioutposty": "987", "DFSTATS_df_referrals": "0", "DFSTATS_df_playerkills": "0", "DFSTATS_df_expdeath": "4498000", "DFSTATS_df_expdeathrecord": "19674301", "DFSTATS_df_expdeathrecord_weekly": "19674301", "DFSTATS_df_expdeath_weekly": "4498000", "DFSTATS_df_expdeathrecord_daily": "4498000", "DFSTATS_df_expdeath_daily": "4498000", "DFSTATS_df_loots_daily": "314", "DFSTATS_df_loots_weekly": "4824", "DFSTATS_df_loots_total": "8087", "DFSTATS_df_playerkills_weekly": "0", "DFSTATS_df_playerkills_daily": "0", "DFSTATS_df_itime": "0", "DFSTATS_df_bonustime": "0", "DFSTATS_df_refertime": "0", "DFSTATS_df_hungertime": "556608733", "DFSTATS_df_lastspawntime": "0", "DFSTATS_df_servertime": "556608733", "DFSTATS_df_creationtime": "555553063", "DFSTATS_df_buildingexp": "0", "DFSTATS_df_buildingchanges": "0", "DFSTATS_df_mission1_title": "", "DFSTATS_df_mission1_exp": "0", "DFSTATS_df_mission1_cash": "0", "DFSTATS_df_mission1_type": "", "DFSTATS_df_mission1_targetloc": "", "DFSTATS_df_mission1_targettype": "", "DFSTATS_df_mission1_targetquantity": "0", "DFSTATS_df_mission1_stage": "0", "DFSTATS_df_mission2_title": "", "DFSTATS_df_mission2_exp": "0", "DFSTATS_df_mission2_cash": "0", "DFSTATS_df_mission2_type": "", "DFSTATS_df_mission2_targetloc": "", "DFSTATS_df_mission2_targettype": "", "DFSTATS_df_mission2_targetquantity": "0", "DFSTATS_df_mission2_stage": "0", "DFSTATS_df_mission3_title": "", "DFSTATS_df_mission3_exp": "0", "DFSTATS_df_mission3_cash": "0", "DFSTATS_df_mission3_type": "", "DFSTATS_df_mission3_targetloc": "", "DFSTATS_df_mission3_targettype": "", "DFSTATS_df_mission3_targetquantity": "0", "DFSTATS_df_mission3_stage": "0", "DFSTATS_df_missions_completed": "", "DFSTATS_df_mission1_targetloc2": "", "DFSTATS_df_mission2_targetloc2": "", "DFSTATS_df_mission3_targetloc2": "", "DFSTATS_df_campaign_cp": "", "DFSTATS_df_campaign_date": "", "DFSTATS_df_campaign_site": "", "DFSTATS_df_campaign_sid": "0", "DFSTATS_df_transaction_id": "35588", "DFSTATS_df_avatar_face": "7", "DFSTATS_df_avatar_skin_colour": "tan", "DFSTATS_df_avatar_beard": "stubble", "DFSTATS_df_avatar_hair": "short", "DFSTATS_df_avatar_hair_colour": "Black", "DFSTATS_df_avatar_mask": "exterminatorhelmet_colourRed", "DFSTATS_df_avatar_hat": "bobblehat_colourGrey", "DFSTATS_df_avatar_shirt": "vest_colourWhite", "DFSTATS_df_avatar_trousers": "combats_colourGrey", "DFSTATS_df_avatar_coat": "", "DFSTATS_df_see_surgeon": "0", "DFSTATS_df_avatar_weapon1": "riflewood", "DFSTATS_df_avatar_weapon2": "sword", "DFSTATS_df_avatar_weapon3": "wood", "DFSTATS_df_storage_slots": "465", "DFSTATS_df_looteditem_type": "", "DFSTATS_df_looteditem_pos": "0", "DFSTATS_df_looteditem_time": "556580791", "DFSTATS_df_looteditem_quantity": "0", "DFSTATS_df_looteditem_event_id": "0", "DFSTATS_df_gameversion": "1", "DFSTATS_df_session3d": "406", "DFSTATS_df_ghostreset": "0", "DFSTATS_df_prearena_hp": "0", "DFSTATS_df_prearena_armour": "0", "DFSTATS_df_boostspeeduntil": "0", "DFSTATS_df_boostexpuntil": "1756057170", "DFSTATS_df_boostdamageuntil": "1756012507", "DFSTATS_df_boostspeeduntil_ex": "0", "DFSTATS_df_boostdamageuntil_ex": "0", "DFSTATS_df_boostexpuntil_ex": "0", "DFSTATS_df_building_type": "", "DFSTATS_df_building_id": "0", "DFSTATS_df_building_orientation": "0", "DFSTATS_df_perm_secretshop": "0", "DFSTATS_df_clan_id": "-1", "DFSTATS_df_clan_name": "", "DFSTATS_df_clan_rank": "", "DFSTATS_df_clan_secretshop": "0", "DFSTATS_df_clan_last_join_time": "1756066362", "DFSTATS_df_implant1_type": "quartermasterim<PERSON>lant", "DFSTATS_df_implant2_type": "prophecyimplant", "DFSTATS_df_implant3_type": "mimicimplant", "DFSTATS_df_implant4_type": "mastercraftimplant", "DFSTATS_df_implant5_type": "runnerimplant", "DFSTATS_df_implant6_type": "secrolink_nt", "DFSTATS_df_implant7_type": "volatiledevilimplant", "DFSTATS_df_implant8_type": "rageimplant", "DFSTATS_df_implant9_type": "", "DFSTATS_df_implant10_type": "", "DFSTATS_df_implant11_type": "", "DFSTATS_df_implant12_type": "", "DFSTATS_df_implant13_type": "", "DFSTATS_df_implant14_type": "", "DFSTATS_df_implant15_type": "", "DFSTATS_df_implant16_type": "", "DFSTATS_df_implant17_type": "", "DFSTATS_df_implant18_type": "", "DFSTATS_df_implant19_type": "", "DFSTATS_df_implant20_type": "", "DFSTATS_df_implantslots": 8, "DFSTATS_df_block_support_until": "0", "DFSTATS_df_last_daily_tpk_win": "0", "DFSTATS_df_hidearmour": "0", "DFSTATS_df_seasonalgifts": "0", "DFSTATS_df_freestatreset": "0", "DFSTATS_df_freelevel50statreset": "1", "DFSTATS_df_fasttravelpermissions": "63", "DFSTATS_df_backpack": "smallbackpack_stats2", "DFSTATS_df_lastdailywin": "0", "DFSTATS_df_secret_shop_cooldown_until": "0", "DFSTATS_df_backpack1_type": "extralargesecuritybox", "DFSTATS_df_backpack1_quantity": "1", "DFSTATS_df_backpack2_type": "barricadekit", "DFSTATS_df_backpack2_quantity": "1", "DFSTATS_df_backpack3_type": "barricadekit", "DFSTATS_df_backpack3_quantity": "1", "DFSTATS_df_backpack4_type": "clawhammer", "DFSTATS_df_backpack4_quantity": "1", "DFSTATS_df_backpack5_type": "heavygrenadeammo", "DFSTATS_df_backpack5_quantity": "400", "PLAYERVARS_implant_storage_slots": "20", "PLAYERVARS_implant_preset_slots": "3", "GLOBALDATA_professions_doctor_strength": "0", "GLOBALDATA_professions_doctor_agility": "0", "GLOBALDATA_professions_doctor_accuracy": "0", "GLOBALDATA_professions_doctor_endurance": "0", "GLOBALDATA_professions_doctor_criticalhit": "0", "GLOBALDATA_professions_doctor_reloading": "0", "GLOBALDATA_professions_doctor_dexterity": "0", "GLOBALDATA_professions_doctor_survival": "0", "GLOBALDATA_professions_doctor_evasion": "0", "GLOBALDATA_professions_doctor_promelee": "0", "GLOBALDATA_professions_doctor_propistol": "0", "GLOBALDATA_professions_doctor_proshotgun": "0", "GLOBALDATA_professions_doctor_promachinegun": "0", "GLOBALDATA_professions_doctor_proexplosive": "0", "GLOBALDATA_professions_doctor_prorifle": "0", "GLOBALDATA_professions_doctor_givemedicene": "1", "GLOBALDATA_professions_doctor_givewoodandnails": "0", "GLOBALDATA_professions_doctor_repairarmour": "0", "GLOBALDATA_professions_doctor_cookfood": "0", "GLOBALDATA_professions_doctor_growfood": "0", "GLOBALDATA_professions_doctor_growdrugs": "0", "GLOBALDATA_professions_doctor_expdiff": "0", "GLOBALDATA_professions_doctor_hasweapon": "", "GLOBALDATA_professions_doctor_hasweaponname": "", "GLOBALDATA_professions_doctor_hasitem": "adrenaline_nt", "GLOBALDATA_professions_doctor_hasitemname": "Adrenaline", "GLOBALDATA_professions_doctor_bonuscash": "250", "GLOBALDATA_professions_doctor_shirt": "shirt_colour<PERSON>hite", "GLOBALDATA_professions_doctor_trousers": "trousers_colourWhite", "GLOBALDATA_professions_doctor_description": "- Can administer medicines for enhanced health restoration.", "GLOBALDATA_professions_doctor_details": "<font color=\"red\">Service Details</font> <br />Doctors can sell their healing services to other players on the marketplace. <br /><br />When a doctor is hired, their hunger will decrease with each service. <br /><br />Services will not be sold if in \"Critical\" health or \"Starving\" hunger status conditions. <br /><br />Doctors can use their own services on themselves in their inventory.", "GLOBALDATA_professions_engineer_strength": "0", "GLOBALDATA_professions_engineer_agility": "0", "GLOBALDATA_professions_engineer_accuracy": "0", "GLOBALDATA_professions_engineer_endurance": "0", "GLOBALDATA_professions_engineer_criticalhit": "0", "GLOBALDATA_professions_engineer_reloading": "0", "GLOBALDATA_professions_engineer_dexterity": "0", "GLOBALDATA_professions_engineer_survival": "0", "GLOBALDATA_professions_engineer_evasion": "0", "GLOBALDATA_professions_engineer_promelee": "0", "GLOBALDATA_professions_engineer_propistol": "0", "GLOBALDATA_professions_engineer_proshotgun": "0", "GLOBALDATA_professions_engineer_promachinegun": "0", "GLOBALDATA_professions_engineer_proexplosive": "0", "GLOBALDATA_professions_engineer_prorifle": "0", "GLOBALDATA_professions_engineer_givemedicene": "0", "GLOBALDATA_professions_engineer_givewoodandnails": "0", "GLOBALDATA_professions_engineer_repairarmour": "1", "GLOBALDATA_professions_engineer_cookfood": "0", "GLOBALDATA_professions_engineer_growfood": "0", "GLOBALDATA_professions_engineer_growdrugs": "0", "GLOBALDATA_professions_engineer_expdiff": "0", "GLOBALDATA_professions_engineer_hasweapon": "", "GLOBALDATA_professions_engineer_hasweaponname": "", "GLOBALDATA_professions_engineer_hasitem": "", "GLOBALDATA_professions_engineer_hasitemname": "", "GLOBALDATA_professions_engineer_bonuscash": "250", "GLOBALDATA_professions_engineer_shirt": "shirt_colourBlue", "GLOBALDATA_professions_engineer_trousers": "trousers_colourBlack", "GLOBALDATA_professions_engineer_description": "- Can repair armour.", "GLOBALDATA_professions_engineer_details": "<font color=\"red\">Service Details</font> <br />Engineers can sell their armor repair services to other players on the marketplace. <br /><br />When an engineer is hired, their hunger will decrease with each service. <br /><br />Services will not be sold if in \"Critical\" health or \"Starving\" hunger status conditions. <br /><br />Engineers can use their own repair services in their inventory.", "GLOBALDATA_professions_chef_strength": "0", "GLOBALDATA_professions_chef_agility": "0", "GLOBALDATA_professions_chef_accuracy": "0", "GLOBALDATA_professions_chef_endurance": "0", "GLOBALDATA_professions_chef_criticalhit": "0", "GLOBALDATA_professions_chef_reloading": "0", "GLOBALDATA_professions_chef_dexterity": "0", "GLOBALDATA_professions_chef_survival": "0", "GLOBALDATA_professions_chef_evasion": "0", "GLOBALDATA_professions_chef_promelee": "0", "GLOBALDATA_professions_chef_propistol": "0", "GLOBALDATA_professions_chef_proshotgun": "0", "GLOBALDATA_professions_chef_promachinegun": "0", "GLOBALDATA_professions_chef_proexplosive": "0", "GLOBALDATA_professions_chef_prorifle": "0", "GLOBALDATA_professions_chef_givemedicene": "0", "GLOBALDATA_professions_chef_givewoodandnails": "0", "GLOBALDATA_professions_chef_repairarmour": "0", "GLOBALDATA_professions_chef_cookfood": "1", "GLOBALDATA_professions_chef_growfood": "0", "GLOBALDATA_professions_chef_growdrugs": "0", "GLOBALDATA_professions_chef_expdiff": "0", "GLOBALDATA_professions_chef_hasweapon": "chefknife2", "GLOBALDATA_professions_chef_hasweaponname": "Chef Knife", "GLOBALDATA_professions_chef_hasitem": "", "GLOBALDATA_professions_chef_hasitemname": "", "GLOBALDATA_professions_chef_bonuscash": "250", "GLOBALDATA_professions_chef_shirt": "tshirt_colourWhite", "GLOBALDATA_professions_chef_trousers": "trousers_colourWhite", "GLOBALDATA_professions_chef_description": "- Cooks food for enhanced hunger restoration.", "GLOBALDATA_professions_chef_details": "<font color=\"red\">Service Details</font> <br />Chefs can sell their cooking services to other players on the marketplace. <br /><br />When food is cooked it restores more hunger. <br /><br />When a chef is hired, their hunger will decrease with each service. <br /><br />Services will not be sold if in \"Critical\" health or \"Starving\" hunger status conditions. <br /><br />Chefs can use their own cooking services in their inventory.", "GLOBALDATA_professions_scientist_strength": "0", "GLOBALDATA_professions_scientist_agility": "0", "GLOBALDATA_professions_scientist_accuracy": "0", "GLOBALDATA_professions_scientist_endurance": "0", "GLOBALDATA_professions_scientist_criticalhit": "0", "GLOBALDATA_professions_scientist_reloading": "0", "GLOBALDATA_professions_scientist_dexterity": "0", "GLOBALDATA_professions_scientist_survival": "0", "GLOBALDATA_professions_scientist_evasion": "0", "GLOBALDATA_professions_scientist_promelee": "0", "GLOBALDATA_professions_scientist_propistol": "0", "GLOBALDATA_professions_scientist_proshotgun": "0", "GLOBALDATA_professions_scientist_promachinegun": "0", "GLOBALDATA_professions_scientist_proexplosive": "0", "GLOBALDATA_professions_scientist_prorifle": "0", "GLOBALDATA_professions_scientist_givemedicene": "0", "GLOBALDATA_professions_scientist_givewoodandnails": "0", "GLOBALDATA_professions_scientist_repairarmour": "0", "GLOBALDATA_professions_scientist_cookfood": "0", "GLOBALDATA_professions_scientist_growfood": "0", "GLOBALDATA_professions_scientist_growdrugs": "1", "GLOBALDATA_professions_scientist_expdiff": "0", "GLOBALDATA_professions_scientist_hasweapon": "", "GLOBALDATA_professions_scientist_hasweaponname": "", "GLOBALDATA_professions_scientist_hasitem": "", "GLOBALDATA_professions_scientist_hasitemname": "", "GLOBALDATA_professions_scientist_bonuscash": "250", "GLOBALDATA_professions_scientist_shirt": "shirt_colour<PERSON>hite", "GLOBALDATA_professions_scientist_trousers": "trousers_colourWhite", "GLOBALDATA_professions_scientist_description": "- Creates complex medical treatments daily.", "GLOBALDATA_professions_scientist_details": "<font color=\"red\">Production Details</font> <br />Every 24 hours, upon logging in, Scientists will generate 2-4 medicine healing items. <br /><br />Items will not be produced if in \"Critical\" health or \"Starving\" hunger status conditions. <br /><br /> You must have at least 4 empty inventory slots in order to create items.", "GLOBALDATA_professions_farmer_strength": "0", "GLOBALDATA_professions_farmer_agility": "0", "GLOBALDATA_professions_farmer_accuracy": "0", "GLOBALDATA_professions_farmer_endurance": "0", "GLOBALDATA_professions_farmer_criticalhit": "0", "GLOBALDATA_professions_farmer_reloading": "0", "GLOBALDATA_professions_farmer_dexterity": "0", "GLOBALDATA_professions_farmer_survival": "0", "GLOBALDATA_professions_farmer_evasion": "0", "GLOBALDATA_professions_farmer_promelee": "0", "GLOBALDATA_professions_farmer_propistol": "0", "GLOBALDATA_professions_farmer_proshotgun": "0", "GLOBALDATA_professions_farmer_promachinegun": "0", "GLOBALDATA_professions_farmer_proexplosive": "0", "GLOBALDATA_professions_farmer_prorifle": "0", "GLOBALDATA_professions_farmer_givemedicene": "0", "GLOBALDATA_professions_farmer_givewoodandnails": "0", "GLOBALDATA_professions_farmer_repairarmour": "0", "GLOBALDATA_professions_farmer_cookfood": "0", "GLOBALDATA_professions_farmer_growfood": "1", "GLOBALDATA_professions_farmer_growdrugs": "0", "GLOBALDATA_professions_farmer_expdiff": "0", "GLOBALDATA_professions_farmer_hasweapon": "mini412", "GLOBALDATA_professions_farmer_hasweaponname": "Mini-41 Rifle", "GLOBALDATA_professions_farmer_hasitem": "", "GLOBALDATA_professions_farmer_hasitemname": "", "GLOBALDATA_professions_farmer_bonuscash": "250", "GLOBALDATA_professions_farmer_shirt": "tshirt_colourWhite", "GLOBALDATA_professions_farmer_trousers": "jeans_colourBlue", "GLOBALDATA_professions_farmer_description": "- Creates farm fresh food daily.", "GLOBALDATA_professions_farmer_details": "<font color=\"red\">Production Details</font> <br />Every 24 hours, upon logging in, Farmers will generate 2-4 nutritional food items. <br /><br />Items will not be produced if in \"Critical\" health or \"Starving\" hunger status conditions. <br /><br /> You must have at least 4 empty inventory slots in order to create items.", "GLOBALDATA_professions_craftsman_strength": "0", "GLOBALDATA_professions_craftsman_agility": "0", "GLOBALDATA_professions_craftsman_accuracy": "0", "GLOBALDATA_professions_craftsman_endurance": "0", "GLOBALDATA_professions_craftsman_criticalhit": "0", "GLOBALDATA_professions_craftsman_reloading": "0", "GLOBALDATA_professions_craftsman_dexterity": "0", "GLOBALDATA_professions_craftsman_survival": "0", "GLOBALDATA_professions_craftsman_evasion": "0", "GLOBALDATA_professions_craftsman_promelee": "0", "GLOBALDATA_professions_craftsman_propistol": "0", "GLOBALDATA_professions_craftsman_proshotgun": "0", "GLOBALDATA_professions_craftsman_promachinegun": "0", "GLOBALDATA_professions_craftsman_proexplosive": "0", "GLOBALDATA_professions_craftsman_prorifle": "0", "GLOBALDATA_professions_craftsman_givemedicene": "0", "GLOBALDATA_professions_craftsman_givewoodandnails": "1", "GLOBALDATA_professions_craftsman_repairarmour": "0", "GLOBALDATA_professions_craftsman_cookfood": "0", "GLOBALDATA_professions_craftsman_growfood": "0", "GLOBALDATA_professions_craftsman_growdrugs": "0", "GLOBALDATA_professions_craftsman_expdiff": "0", "GLOBALDATA_professions_craftsman_hasweapon": "", "GLOBALDATA_professions_craftsman_hasweaponname": "", "GLOBALDATA_professions_craftsman_hasitem": "clawhammer", "GLOBALDATA_professions_craftsman_hasitemname": "<PERSON><PERSON>", "GLOBALDATA_professions_craftsman_bonuscash": "250", "GLOBALDATA_professions_craftsman_shirt": "shirt_colourBlue", "GLOBALDATA_professions_craftsman_trousers": "trousers_colourBlack", "GLOBALDATA_professions_craftsman_description": "- Creates materials used for crafting daily.", "GLOBALDATA_professions_craftsman_details": "<font color=\"red\">Production Details</font> <br />Craftsman have more crafting recipes available by default. <br /><br />Every 24 hours, upon logging in, Craftsman will generate 2-4 crafting recipe items. <br /><br />Items will not be produced if in \"Critical\" health or \"Starving\" hunger status conditions. <br /><br /> You must have at least 4 empty inventory slots in order to create items.", "GLOBALDATA_professions_weaponsmith_strength": "0", "GLOBALDATA_professions_weaponsmith_agility": "0", "GLOBALDATA_professions_weaponsmith_accuracy": "0", "GLOBALDATA_professions_weaponsmith_endurance": "0", "GLOBALDATA_professions_weaponsmith_criticalhit": "0", "GLOBALDATA_professions_weaponsmith_reloading": "0", "GLOBALDATA_professions_weaponsmith_dexterity": "0", "GLOBALDATA_professions_weaponsmith_survival": "0", "GLOBALDATA_professions_weaponsmith_evasion": "0", "GLOBALDATA_professions_weaponsmith_promelee": "0", "GLOBALDATA_professions_weaponsmith_propistol": "0", "GLOBALDATA_professions_weaponsmith_proshotgun": "0", "GLOBALDATA_professions_weaponsmith_promachinegun": "0", "GLOBALDATA_professions_weaponsmith_proexplosive": "0", "GLOBALDATA_professions_weaponsmith_prorifle": "0", "GLOBALDATA_professions_weaponsmith_givemedicene": "0", "GLOBALDATA_professions_weaponsmith_givewoodandnails": "1", "GLOBALDATA_professions_weaponsmith_repairarmour": "0", "GLOBALDATA_professions_weaponsmith_cookfood": "0", "GLOBALDATA_professions_weaponsmith_growfood": "0", "GLOBALDATA_professions_weaponsmith_growdrugs": "0", "GLOBALDATA_professions_weaponsmith_expdiff": "0", "GLOBALDATA_professions_weaponsmith_hasweapon": "", "GLOBALDATA_professions_weaponsmith_hasweaponname": "", "GLOBALDATA_professions_weaponsmith_hasitem": "", "GLOBALDATA_professions_weaponsmith_hasitemname": "", "GLOBALDATA_professions_weaponsmith_bonuscash": "250", "GLOBALDATA_professions_weaponsmith_shirt": "vest_colourBlack", "GLOBALDATA_professions_weaponsmith_trousers": "trousers_colourBlack", "GLOBALDATA_professions_weaponsmith_description": "- Creates a random weapon daily.", "GLOBALDATA_professions_weaponsmith_details": "<font color=\"red\">Production Details</font> <br />Every 24 hours, upon logging in, Weaponsmith will generate 1 random weapon. <br /><br />Items will not be produced if in \"Critical\" health or \"Starving\" hunger status conditions. <br /><br /> You must have at least 1 empty inventory slots in order to create weapons.", "GLOBALDATA_professions_boxer_strength": "0", "GLOBALDATA_professions_boxer_agility": "0", "GLOBALDATA_professions_boxer_accuracy": "0", "GLOBALDATA_professions_boxer_endurance": "5", "GLOBALDATA_professions_boxer_criticalhit": "5", "GLOBALDATA_professions_boxer_reloading": "0", "GLOBALDATA_professions_boxer_dexterity": "15", "GLOBALDATA_professions_boxer_survival": "0", "GLOBALDATA_professions_boxer_evasion": "0", "GLOBALDATA_professions_boxer_promelee": "5", "GLOBALDATA_professions_boxer_propistol": "0", "GLOBALDATA_professions_boxer_proshotgun": "0", "GLOBALDATA_professions_boxer_promachinegun": "0", "GLOBALDATA_professions_boxer_proexplosive": "0", "GLOBALDATA_professions_boxer_prorifle": "0", "GLOBALDATA_professions_boxer_givemedicene": "0", "GLOBALDATA_professions_boxer_givewoodandnails": "0", "GLOBALDATA_professions_boxer_repairarmour": "0", "GLOBALDATA_professions_boxer_cookfood": "0", "GLOBALDATA_professions_boxer_growfood": "0", "GLOBALDATA_professions_boxer_growdrugs": "0", "GLOBALDATA_professions_boxer_expdiff": "0", "GLOBALDATA_professions_boxer_hasweapon": "", "GLOBALDATA_professions_boxer_hasweaponname": "", "GLOBALDATA_professions_boxer_hasitem": "", "GLOBALDATA_professions_boxer_hasitemname": "", "GLOBALDATA_professions_boxer_bonuscash": "0", "GLOBALDATA_professions_boxer_shirt": "vest_colourWhite", "GLOBALDATA_professions_boxer_trousers": "jeans_colourBlue", "GLOBALDATA_professions_boxer_description": "<font color=\"#25db00\">+20% Melee Weapon Damage (Non-Chainsaw)<br />+15% Reduced Incoming Damage</font>", "GLOBALDATA_professions_boxer_details": "<font color=\"red\">Weapon Details</font> <br />- Melee weapons are short-range, silent, very high critical hit chance looting weapons.<br /><br /> <font color=\"red\">Stat Details</font> <br />- Endurance increases your base health and sprint stamina.<br /><br />- Critical Hit increases your chances of 5x damage hits.<br /><br />- Dexterity increases your attack speed and weapon maneuvering.", "GLOBALDATA_professions_lumberjack_strength": "10", "GLOBALDATA_professions_lumberjack_agility": "0", "GLOBALDATA_professions_lumberjack_accuracy": "0", "GLOBALDATA_professions_lumberjack_endurance": "5", "GLOBALDATA_professions_lumberjack_criticalhit": "10", "GLOBALDATA_professions_lumberjack_reloading": "0", "GLOBALDATA_professions_lumberjack_dexterity": "0", "GLOBALDATA_professions_lumberjack_survival": "0", "GLOBALDATA_professions_lumberjack_evasion": "0", "GLOBALDATA_professions_lumberjack_promelee": "5", "GLOBALDATA_professions_lumberjack_propistol": "0", "GLOBALDATA_professions_lumberjack_proshotgun": "0", "GLOBALDATA_professions_lumberjack_promachinegun": "0", "GLOBALDATA_professions_lumberjack_proexplosive": "0", "GLOBALDATA_professions_lumberjack_prorifle": "0", "GLOBALDATA_professions_lumberjack_givemedicene": "0", "GLOBALDATA_professions_lumberjack_givewoodandnails": "0", "GLOBALDATA_professions_lumberjack_repairarmour": "0", "GLOBALDATA_professions_lumberjack_cookfood": "0", "GLOBALDATA_professions_lumberjack_growfood": "0", "GLOBALDATA_professions_lumberjack_growdrugs": "0", "GLOBALDATA_professions_lumberjack_expdiff": "0", "GLOBALDATA_professions_lumberjack_hasweapon": "ecocs20v2", "GLOBALDATA_professions_lumberjack_hasweaponname": "ECO CS-20V Chainsaw", "GLOBALDATA_professions_lumberjack_hasitem": "", "GLOBALDATA_professions_lumberjack_hasitemname": "", "GLOBALDATA_professions_lumberjack_bonuscash": "0", "GLOBALDATA_professions_lumberjack_shirt": "shirt_colourRed", "GLOBALDATA_professions_lumberjack_trousers": "jeans_colourBlue", "GLOBALDATA_professions_lumberjack_description": "<font color=\"#25db00\">+20% Chainsaw Weapon Damage<br />+15% Reduced Incoming Damage</font>", "GLOBALDATA_professions_lumberjack_details": "<font color=\"red\">Weapon Details</font> <br />- Chainsaws are short-range high critical hit chance weapons with medium crowd control. <br /><br /> <font color=\"red\">Stat Details</font> <br />- Strength is used to equip heavier armor and weapons such as Chainsaws, Shotguns, Assault Rifles, HMGs, and Miniguns. Heavy armour offers more protection.<br /><br />- Endurance increases your base health and sprint stamina. <br /><br />- Critical Hit increases your chances of 5x damage hits.", "GLOBALDATA_professions_landscaper_strength": "5", "GLOBALDATA_professions_landscaper_agility": "5", "GLOBALDATA_professions_landscaper_accuracy": "0", "GLOBALDATA_professions_landscaper_endurance": "5", "GLOBALDATA_professions_landscaper_criticalhit": "0", "GLOBALDATA_professions_landscaper_reloading": "0", "GLOBALDATA_professions_landscaper_dexterity": "0", "GLOBALDATA_professions_landscaper_survival": "0", "GLOBALDATA_professions_landscaper_evasion": "0", "GLOBALDATA_professions_landscaper_promelee": "10", "GLOBALDATA_professions_landscaper_propistol": "0", "GLOBALDATA_professions_landscaper_proshotgun": "0", "GLOBALDATA_professions_landscaper_promachinegun": "0", "GLOBALDATA_professions_landscaper_proexplosive": "0", "GLOBALDATA_professions_landscaper_prorifle": "0", "GLOBALDATA_professions_landscaper_givemedicene": "0", "GLOBALDATA_professions_landscaper_givewoodandnails": "0", "GLOBALDATA_professions_landscaper_repairarmour": "0", "GLOBALDATA_professions_landscaper_cookfood": "0", "GLOBALDATA_professions_landscaper_growfood": "0", "GLOBALDATA_professions_landscaper_growdrugs": "0", "GLOBALDATA_professions_landscaper_expdiff": "0", "GLOBALDATA_professions_landscaper_hasweapon": "ecocs20v2", "GLOBALDATA_professions_landscaper_hasweaponname": "ECO CS-20V Chainsaw", "GLOBALDATA_professions_landscaper_hasitem": "", "GLOBALDATA_professions_landscaper_hasitemname": "", "GLOBALDATA_professions_landscaper_bonuscash": "0", "GLOBALDATA_professions_landscaper_shirt": "shirt_colour<PERSON><PERSON><PERSON>", "GLOBALDATA_professions_landscaper_trousers": "jeans_colourGreen", "GLOBALDATA_professions_landscaper_description": "<font color=\"#25db00\">+20% Melee Weapon Damage (Non-Chainsaw)<br />+20% Chainsaw Weapon Damage</font>", "GLOBALDATA_professions_landscaper_details": "<font color=\"red\">Weapon Details</font> <br />- Melee weapons are short-range, silent, very high critical hit chance looting weapons.<br /><br />- Chainsaws are short-range high critical hit chance weapons with medium crowd control. <br /><br /> <font color=\"red\">Stat Details</font> <br />- Strength is used to equip heavier armor and weapons such as Chainsaws, Shotguns, Assault Rifles, HMGs, and Miniguns. Heavy armour offers more protection.<br /><br />- Endurance increases your base health and sprint stamina. <br /><br />- Critical Hit increases your chances of 5x damage hits.", "GLOBALDATA_professions_hunter_strength": "0", "GLOBALDATA_professions_hunter_agility": "0", "GLOBALDATA_professions_hunter_accuracy": "15", "GLOBALDATA_professions_hunter_endurance": "0", "GLOBALDATA_professions_hunter_criticalhit": "0", "GLOBALDATA_professions_hunter_reloading": "0", "GLOBALDATA_professions_hunter_dexterity": "0", "GLOBALDATA_professions_hunter_survival": "10", "GLOBALDATA_professions_hunter_evasion": "0", "GLOBALDATA_professions_hunter_promelee": "0", "GLOBALDATA_professions_hunter_propistol": "0", "GLOBALDATA_professions_hunter_proshotgun": "0", "GLOBALDATA_professions_hunter_promachinegun": "0", "GLOBALDATA_professions_hunter_proexplosive": "0", "GLOBALDATA_professions_hunter_prorifle": "10", "GLOBALDATA_professions_hunter_givemedicene": "0", "GLOBALDATA_professions_hunter_givewoodandnails": "0", "GLOBALDATA_professions_hunter_repairarmour": "0", "GLOBALDATA_professions_hunter_cookfood": "0", "GLOBALDATA_professions_hunter_growfood": "0", "GLOBALDATA_professions_hunter_growdrugs": "0", "GLOBALDATA_professions_hunter_expdiff": "0", "GLOBALDATA_professions_hunter_hasweapon": "betarx42", "GLOBALDATA_professions_hunter_hasweaponname": "Beta RX4 Rifle", "GLOBALDATA_professions_hunter_hasitem": "", "GLOBALDATA_professions_hunter_hasitemname": "", "GLOBALDATA_professions_hunter_bonuscash": "0", "GLOBALDATA_professions_hunter_shirt": "tshirt_colourBrown", "GLOBALDATA_professions_hunter_trousers": "jeans_colourBrown", "GLOBALDATA_professions_hunter_description": "<font color=\"#25db00\">+30% Rifle Weapon Damage</font>", "GLOBALDATA_professions_hunter_details": "<font color=\"red\">Weapon Details</font> <br />- Rifles are long-range, powerful but slow firing, very high critical hit chance looting weapons.  <br /><br /> <font color=\"red\">Stat Details</font> <br />- Critical Hit increases your chances of 5x damage hits. ", "GLOBALDATA_professions_survivalist_strength": "5", "GLOBALDATA_professions_survivalist_agility": "5", "GLOBALDATA_professions_survivalist_accuracy": "0", "GLOBALDATA_professions_survivalist_endurance": "5", "GLOBALDATA_professions_survivalist_criticalhit": "0", "GLOBALDATA_professions_survivalist_reloading": "0", "GLOBALDATA_professions_survivalist_dexterity": "0", "GLOBALDATA_professions_survivalist_survival": "0", "GLOBALDATA_professions_survivalist_evasion": "0", "GLOBALDATA_professions_survivalist_promelee": "0", "GLOBALDATA_professions_survivalist_propistol": "0", "GLOBALDATA_professions_survivalist_proshotgun": "10", "GLOBALDATA_professions_survivalist_promachinegun": "0", "GLOBALDATA_professions_survivalist_proexplosive": "0", "GLOBALDATA_professions_survivalist_prorifle": "0", "GLOBALDATA_professions_survivalist_givemedicene": "0", "GLOBALDATA_professions_survivalist_givewoodandnails": "0", "GLOBALDATA_professions_survivalist_repairarmour": "0", "GLOBALDATA_professions_survivalist_cookfood": "0", "GLOBALDATA_professions_survivalist_growfood": "0", "GLOBALDATA_professions_survivalist_growdrugs": "0", "GLOBALDATA_professions_survivalist_expdiff": "0", "GLOBALDATA_professions_survivalist_hasweapon": "mancinim12", "GLOBALDATA_professions_survivalist_hasweaponname": "Mancini Shotgun", "GLOBALDATA_professions_survivalist_hasitem": "", "GLOBALDATA_professions_survivalist_hasitemname": "", "GLOBALDATA_professions_survivalist_bonuscash": "0", "GLOBALDATA_professions_survivalist_shirt": "tshirt_colourBrown", "GLOBALDATA_professions_survivalist_trousers": "jeans_colourBlack", "GLOBALDATA_professions_survivalist_description": "<font color=\"#25db00\">+25% Shotgun Weapon Damage<br />+5% Reduced Incoming Damage</font>", "GLOBALDATA_professions_survivalist_details": "<font color=\"red\">Weapon Details</font> <br />- Shotguns are short-range weapons with high crowd control. Shotguns cannot roll critical hits.<br /><br /> <font color=\"red\">Stat Details</font> <br />- Strength is used to equip heavier armor and weapons such as Chainsaws, Shotguns, Assault Rifles, HMGs, and Miniguns. Heavy armour offers more protection.<br /><br />- Endurance increases your base health and sprint stamina.", "GLOBALDATA_professions_criminal_strength": "0", "GLOBALDATA_professions_criminal_agility": "10", "GLOBALDATA_professions_criminal_accuracy": "0", "GLOBALDATA_professions_criminal_endurance": "0", "GLOBALDATA_professions_criminal_criticalhit": "5", "GLOBALDATA_professions_criminal_reloading": "0", "GLOBALDATA_professions_criminal_dexterity": "0", "GLOBALDATA_professions_criminal_survival": "5", "GLOBALDATA_professions_criminal_evasion": "0", "GLOBALDATA_professions_criminal_promelee": "10", "GLOBALDATA_professions_criminal_propistol": "0", "GLOBALDATA_professions_criminal_proshotgun": "0", "GLOBALDATA_professions_criminal_promachinegun": "0", "GLOBALDATA_professions_criminal_proexplosive": "0", "GLOBALDATA_professions_criminal_prorifle": "0", "GLOBALDATA_professions_criminal_givemedicene": "0", "GLOBALDATA_professions_criminal_givewoodandnails": "0", "GLOBALDATA_professions_criminal_repairarmour": "0", "GLOBALDATA_professions_criminal_cookfood": "0", "GLOBALDATA_professions_criminal_growfood": "0", "GLOBALDATA_professions_criminal_growdrugs": "0", "GLOBALDATA_professions_criminal_expdiff": "0", "GLOBALDATA_professions_criminal_hasweapon": "ironpipe2", "GLOBALDATA_professions_criminal_hasweaponname": "Damaged Iron Pipe", "GLOBALDATA_professions_criminal_hasitem": "", "GLOBALDATA_professions_criminal_hasitemname": "", "GLOBALDATA_professions_criminal_bonuscash": "0", "GLOBALDATA_professions_criminal_shirt": "vest_colourRed", "GLOBALDATA_professions_criminal_trousers": "jeans_colourBlack", "GLOBALDATA_professions_criminal_description": "<font color=\"#25db00\">+20% Loot Search Speed<br />+30% Cash Find Quantity<br />+10% Item Find Chance</font>", "GLOBALDATA_professions_criminal_details": "<font color=\"red\">Weapon Details</font> <br />- Melee weapons are short-range, silent, very high critical hit chance looting weapons.<br /><br /> <font color=\"red\">Stat Details</font> <br />- Critical Hit increases your chances of 5x damage hits. <br /><br />- Survival decreases the distance infected can see or hear you, and increases melee weapon damage (non-Chainsaw). <br /><br /> <font color=\"red\">Boost Details</font> <br />Item Find Chance: Decreases the likelihood of finding nothing when searching an object. It is not a flat bonus but instead multiplies against the base \"find nothing\" chance.", "GLOBALDATA_professions_soldier_strength": "5", "GLOBALDATA_professions_soldier_agility": "0", "GLOBALDATA_professions_soldier_accuracy": "10", "GLOBALDATA_professions_soldier_endurance": "10", "GLOBALDATA_professions_soldier_criticalhit": "0", "GLOBALDATA_professions_soldier_reloading": "10", "GLOBALDATA_professions_soldier_dexterity": "0", "GLOBALDATA_professions_soldier_survival": "0", "GLOBALDATA_professions_soldier_evasion": "0", "GLOBALDATA_professions_soldier_promelee": "0", "GLOBALDATA_professions_soldier_propistol": "0", "GLOBALDATA_professions_soldier_proshotgun": "0", "GLOBALDATA_professions_soldier_promachinegun": "10", "GLOBALDATA_professions_soldier_proexplosive": "0", "GLOBALDATA_professions_soldier_prorifle": "0", "GLOBALDATA_professions_soldier_givemedicene": "0", "GLOBALDATA_professions_soldier_givewoodandnails": "0", "GLOBALDATA_professions_soldier_repairarmour": "0", "GLOBALDATA_professions_soldier_cookfood": "0", "GLOBALDATA_professions_soldier_growfood": "0", "GLOBALDATA_professions_soldier_growdrugs": "0", "GLOBALDATA_professions_soldier_expdiff": "-20", "GLOBALDATA_professions_soldier_hasweapon": "m162", "GLOBALDATA_professions_soldier_hasweaponname": "M16 Machine Gun", "GLOBALDATA_professions_soldier_hasitem": "", "GLOBALDATA_professions_soldier_hasitemname": "", "GLOBALDATA_professions_soldier_bonuscash": "0", "GLOBALDATA_professions_soldier_shirt": "tshirt_colourGreen", "GLOBALDATA_professions_soldier_trousers": "trousers_colourGreen", "GLOBALDATA_professions_soldier_description": "<font color=\"#25db00\">+30% Machine Gun (Non-SMG) Weapon Damage</font>", "GLOBALDATA_professions_soldier_details": "<font color=\"red\">Weapon Details</font> <br />- Assault Rifle are long-range low critical hit chance weapons with medium crowd control.<br /><br />- HMG and Minigun weapons are medium-range very low critical hit chance heavy crowd control.<br /><br /> <font color=\"red\">Stat Details</font> <br />- Strength is used to equip heavier armor and weapons such as Chainsaws, Shotguns, Assault Rifles, HMGs, and Miniguns. Heavy armour offers more protection.<br /><br />- Endurance increases your base health and sprint stamina.", "GLOBALDATA_professions_grenadier_strength": "0", "GLOBALDATA_professions_grenadier_agility": "0", "GLOBALDATA_professions_grenadier_accuracy": "10", "GLOBALDATA_professions_grenadier_endurance": "0", "GLOBALDATA_professions_grenadier_criticalhit": "0", "GLOBALDATA_professions_grenadier_reloading": "10", "GLOBALDATA_professions_grenadier_dexterity": "0", "GLOBALDATA_professions_grenadier_survival": "0", "GLOBALDATA_professions_grenadier_evasion": "0", "GLOBALDATA_professions_grenadier_promelee": "0", "GLOBALDATA_professions_grenadier_propistol": "0", "GLOBALDATA_professions_grenadier_proshotgun": "0", "GLOBALDATA_professions_grenadier_promachinegun": "0", "GLOBALDATA_professions_grenadier_proexplosive": "10", "GLOBALDATA_professions_grenadier_prorifle": "0", "GLOBALDATA_professions_grenadier_givemedicene": "0", "GLOBALDATA_professions_grenadier_givewoodandnails": "0", "GLOBALDATA_professions_grenadier_repairarmour": "0", "GLOBALDATA_professions_grenadier_cookfood": "0", "GLOBALDATA_professions_grenadier_growfood": "0", "GLOBALDATA_professions_grenadier_growdrugs": "0", "GLOBALDATA_professions_grenadier_expdiff": "0", "GLOBALDATA_professions_grenadier_hasweapon": "m792", "GLOBALDATA_professions_grenadier_hasweaponname": "M79 Grenade Launcher", "GLOBALDATA_professions_grenadier_hasitem": "", "GLOBALDATA_professions_grenadier_hasitemname": "", "GLOBALDATA_professions_grenadier_bonuscash": "0", "GLOBALDATA_professions_grenadier_shirt": "tshirt_colourGreen", "GLOBALDATA_professions_grenadier_trousers": "trousers_colourBrown", "GLOBALDATA_professions_grenadier_description": "<font color=\"#25db00\">+30% Explosive Weapon Damage</font>", "GLOBALDATA_professions_grenadier_details": "<font color=\"red\">Weapon Details</font> <br />- Grenade Launchers are long-range  weapons with medium crowd control. Their alt fire (Q key) offers high utility as it can be shot over walls.<br /><br />- Flamethrowers are medium-range explosive weapons with low crowd control but high damage. <br /><br />Explosive weapons cannot roll critical hits but have group AoE damage.", "GLOBALDATA_professions_police officer_strength": "0", "GLOBALDATA_professions_police officer_agility": "0", "GLOBALDATA_professions_police officer_accuracy": "10", "GLOBALDATA_professions_police officer_endurance": "0", "GLOBALDATA_professions_police officer_criticalhit": "0", "GLOBALDATA_professions_police officer_reloading": "5", "GLOBALDATA_professions_police officer_dexterity": "0", "GLOBALDATA_professions_police officer_survival": "0", "GLOBALDATA_professions_police officer_evasion": "0", "GLOBALDATA_professions_police officer_promelee": "0", "GLOBALDATA_professions_police officer_propistol": "10", "GLOBALDATA_professions_police officer_proshotgun": "10", "GLOBALDATA_professions_police officer_promachinegun": "0", "GLOBALDATA_professions_police officer_proexplosive": "0", "GLOBALDATA_professions_police officer_prorifle": "0", "GLOBALDATA_professions_police officer_givemedicene": "0", "GLOBALDATA_professions_police officer_givewoodandnails": "0", "GLOBALDATA_professions_police officer_repairarmour": "0", "GLOBALDATA_professions_police officer_cookfood": "0", "GLOBALDATA_professions_police officer_growfood": "0", "GLOBALDATA_professions_police officer_growdrugs": "0", "GLOBALDATA_professions_police officer_expdiff": "0", "GLOBALDATA_professions_police officer_hasarmour": "", "GLOBALDATA_professions_police officer_hasarmourname": "", "GLOBALDATA_professions_police officer_hasweapon": "mancinim12", "GLOBALDATA_professions_police officer_hasweaponname": "Mancini Shotgun", "GLOBALDATA_professions_police officer_hasitem": "", "GLOBALDATA_professions_police officer_hasitemname": "", "GLOBALDATA_professions_police officer_bonuscash": "0", "GLOBALDATA_professions_police officer_shirt": "shirt_colourBlue", "GLOBALDATA_professions_police officer_trousers": "trousers_colourBlack", "GLOBALDATA_professions_police officer_description": "<font color=\"#25db00\">+15% Pistol Weapon Damage<br />+15% Shotgun Weapon Damage</font>", "GLOBALDATA_professions_police officer_details": "<font color=\"red\">Weapon Details</font> <br />- Pistols are well rounded medium-range high critical hit looting weapons. <br /><br />- Shotguns are short-range crowd control weapons. Shotguns cannot roll critical hits.", "GLOBALDATA_professions_swat_strength": "0", "GLOBALDATA_professions_swat_agility": "0", "GLOBALDATA_professions_swat_accuracy": "5", "GLOBALDATA_professions_swat_endurance": "0", "GLOBALDATA_professions_swat_criticalhit": "10", "GLOBALDATA_professions_swat_reloading": "0", "GLOBALDATA_professions_swat_dexterity": "5", "GLOBALDATA_professions_swat_survival": "0", "GLOBALDATA_professions_swat_evasion": "0", "GLOBALDATA_professions_swat_promelee": "0", "GLOBALDATA_professions_swat_propistol": "0", "GLOBALDATA_professions_swat_proshotgun": "0", "GLOBALDATA_professions_swat_promachinegun": "10", "GLOBALDATA_professions_swat_proexplosive": "0", "GLOBALDATA_professions_swat_prorifle": "0", "GLOBALDATA_professions_swat_givemedicene": "0", "GLOBALDATA_professions_swat_givewoodandnails": "0", "GLOBALDATA_professions_swat_repairarmour": "0", "GLOBALDATA_professions_swat_cookfood": "0", "GLOBALDATA_professions_swat_growfood": "0", "GLOBALDATA_professions_swat_growdrugs": "0", "GLOBALDATA_professions_swat_expdiff": "0", "GLOBALDATA_professions_swat_hasarmour": "", "GLOBALDATA_professions_swat_hasarmourname": "", "GLOBALDATA_professions_swat_hasweapon": "skorpion2", "GLOBALDATA_professions_swat_hasweaponname": "Skorpion SMG", "GLOBALDATA_professions_swat_hasitem": "", "GLOBALDATA_professions_swat_hasitemname": "", "GLOBALDATA_professions_swat_bonuscash": "0", "GLOBALDATA_professions_swat_shirt": "tshirt_colourBlack", "GLOBALDATA_professions_swat_trousers": "trousers_colourBlack", "GLOBALDATA_professions_swat_description": "<font color=\"#25db00\">+30% Sub Machine Gun Weapon Damage</font>", "GLOBALDATA_professions_swat_details": "<font color=\"red\">Weapon Details</font> <br /> - Submachine Guns are well rounded medium-range, average critical hit chance, medium crowd control weapons. <br /><br /> <font color=\"red\">Stat Details</font> <br />- Critical Hit increases your chances of 5x damage hits. <br /><br />- Dexterity increases your attack speed and weapon maneuvering.", "GLOBALDATA_professions_fireman_strength": "0", "GLOBALDATA_professions_fireman_agility": "0", "GLOBALDATA_professions_fireman_accuracy": "0", "GLOBALDATA_professions_fireman_endurance": "20", "GLOBALDATA_professions_fireman_criticalhit": "0", "GLOBALDATA_professions_fireman_reloading": "0", "GLOBALDATA_professions_fireman_dexterity": "0", "GLOBALDATA_professions_fireman_survival": "0", "GLOBALDATA_professions_fireman_evasion": "0", "GLOBALDATA_professions_fireman_promelee": "10", "GLOBALDATA_professions_fireman_propistol": "0", "GLOBALDATA_professions_fireman_proshotgun": "0", "GLOBALDATA_professions_fireman_promachinegun": "0", "GLOBALDATA_professions_fireman_proexplosive": "0", "GLOBALDATA_professions_fireman_prorifle": "0", "GLOBALDATA_professions_fireman_givemedicene": "0", "GLOBALDATA_professions_fireman_givewoodandnails": "0", "GLOBALDATA_professions_fireman_repairarmour": "0", "GLOBALDATA_professions_fireman_cookfood": "0", "GLOBALDATA_professions_fireman_growfood": "0", "GLOBALDATA_professions_fireman_growdrugs": "0", "GLOBALDATA_professions_fireman_expdiff": "0", "GLOBALDATA_professions_fireman_hasweapon": "fireaxe2", "GLOBALDATA_professions_fireman_hasweaponname": "Damaged Fire Axe", "GLOBALDATA_professions_fireman_hasitem": "", "GLOBALDATA_professions_fireman_hasitemname": "", "GLOBALDATA_professions_fireman_bonuscash": "0", "GLOBALDATA_professions_fireman_shirt": "vest_colourWhite", "GLOBALDATA_professions_fireman_trousers": "jeans_colourBlue", "GLOBALDATA_professions_fireman_description": "<font color=\"#25db00\">+30% Melee Weapon Damage (Non-Chainsaw)</font>", "GLOBALDATA_professions_fireman_details": "<font color=\"red\">Weapon Details</font> <br />- Melee weapons are short-range, silent, very high critical hit chance looting weapons.<br /><br /> <font color=\"red\">Stat Details</font> <br />- Strength is used to equip heavier armor and weapons such as Chainsaws, Shotguns, Assault Rifles, HMGs, and Miniguns. Heavy armour offers more protection.<br /><br />- Endurance increases your base health and sprint stamina.", "GLOBALDATA_professions_investigator_strength": "0", "GLOBALDATA_professions_investigator_agility": "0", "GLOBALDATA_professions_investigator_accuracy": "0", "GLOBALDATA_professions_investigator_endurance": "0", "GLOBALDATA_professions_investigator_criticalhit": "0", "GLOBALDATA_professions_investigator_reloading": "0", "GLOBALDATA_professions_investigator_dexterity": "0", "GLOBALDATA_professions_investigator_survival": "25", "GLOBALDATA_professions_investigator_evasion": "0", "GLOBALDATA_professions_investigator_promelee": "0", "GLOBALDATA_professions_investigator_propistol": "5", "GLOBALDATA_professions_investigator_proshotgun": "0", "GLOBALDATA_professions_investigator_promachinegun": "0", "GLOBALDATA_professions_investigator_proexplosive": "0", "GLOBALDATA_professions_investigator_prorifle": "0", "GLOBALDATA_professions_investigator_givemedicene": "0", "GLOBALDATA_professions_investigator_givewoodandnails": "0", "GLOBALDATA_professions_investigator_repairarmour": "0", "GLOBALDATA_professions_investigator_cookfood": "0", "GLOBALDATA_professions_investigator_growfood": "0", "GLOBALDATA_professions_investigator_growdrugs": "0", "GLOBALDATA_professions_investigator_expdiff": "0", "GLOBALDATA_professions_investigator_hasweapon": "", "GLOBALDATA_professions_investigator_hasweaponname": "", "GLOBALDATA_professions_investigator_hasitem": "", "GLOBALDATA_professions_investigator_hasitemname": "", "GLOBALDATA_professions_investigator_bonuscash": "0", "GLOBALDATA_professions_investigator_shirt": "tshirt_colourBlack", "GLOBALDATA_professions_investigator_trousers": "jeans_colourBlue", "GLOBALDATA_professions_investigator_description": "<font color=\"#25db00\">+15% Pistol Weapon Damage<br />+20% Loot Search Speed<br />+10% Misc. Find Chance<br />+5% Loot Spot Count</font>", "GLOBALDATA_professions_investigator_details": "<font color=\"red\">Weapon Details</font> <br />- Pistols are well rounded medium-range high critical hit looting weapons. <br /><br /> <font color=\"red\">Stat Details</font> <br />- Critical Hit increases your chances of 5x damage hits. <br /><br />- Survival decreases the distance infected can see or hear you, and increases melee weapon damage (non-Chainsaw). <br /><br /> <font color=\"red\">Boost Details</font> <br />- Misc. Chance increases your odds of finding treasure and blueprints. <br /><br />- Loot Spot count increases how many searchable objects are generated in an area.", "GLOBALDATA_professions_guard_strength": "0", "GLOBALDATA_professions_guard_agility": "5", "GLOBALDATA_professions_guard_accuracy": "5", "GLOBALDATA_professions_guard_endurance": "0", "GLOBALDATA_professions_guard_criticalhit": "0", "GLOBALDATA_professions_guard_reloading": "0", "GLOBALDATA_professions_guard_dexterity": "20", "GLOBALDATA_professions_guard_survival": "0", "GLOBALDATA_professions_guard_evasion": "0", "GLOBALDATA_professions_guard_promelee": "0", "GLOBALDATA_professions_guard_propistol": "0", "GLOBALDATA_professions_guard_proshotgun": "0", "GLOBALDATA_professions_guard_promachinegun": "0", "GLOBALDATA_professions_guard_proexplosive": "0", "GLOBALDATA_professions_guard_prorifle": "0", "GLOBALDATA_professions_guard_givemedicene": "0", "GLOBALDATA_professions_guard_givewoodandnails": "0", "GLOBALDATA_professions_guard_repairarmour": "0", "GLOBALDATA_professions_guard_cookfood": "0", "GLOBALDATA_professions_guard_growfood": "0", "GLOBALDATA_professions_guard_growdrugs": "0", "GLOBALDATA_professions_guard_expdiff": "0", "GLOBALDATA_professions_guard_hasweapon": "", "GLOBALDATA_professions_guard_hasweaponname": "", "GLOBALDATA_professions_guard_hasitem": "", "GLOBALDATA_professions_guard_hasitemname": "", "GLOBALDATA_professions_guard_bonuscash": "0", "GLOBALDATA_professions_guard_shirt": "shirt_colour<PERSON>hite", "GLOBALDATA_professions_guard_trousers": "jeans_colourBlack", "GLOBALDATA_professions_guard_description": "<font color=\"#25db00\">+30% Pistol Weapon Damage</font>", "GLOBALDATA_professions_guard_details": "<font color=\"red\">Weapon Details</font> <br />- Pistols are well rounded medium-range high critical hit looting weapons.  <br /><br /> <font color=\"red\">Stat Details</font> <br />- Dexterity increases your attack speed and weapon maneuvering.", "GLOBALDATA_professions_athlete_strength": "0", "GLOBALDATA_professions_athlete_agility": "25", "GLOBALDATA_professions_athlete_accuracy": "0", "GLOBALDATA_professions_athlete_endurance": "0", "GLOBALDATA_professions_athlete_criticalhit": "0", "GLOBALDATA_professions_athlete_reloading": "0", "GLOBALDATA_professions_athlete_dexterity": "0", "GLOBALDATA_professions_athlete_survival": "0", "GLOBALDATA_professions_athlete_evasion": "0", "GLOBALDATA_professions_athlete_promelee": "0", "GLOBALDATA_professions_athlete_propistol": "0", "GLOBALDATA_professions_athlete_proshotgun": "0", "GLOBALDATA_professions_athlete_promachinegun": "0", "GLOBALDATA_professions_athlete_proexplosive": "0", "GLOBALDATA_professions_athlete_prorifle": "0", "GLOBALDATA_professions_athlete_givemedicene": "0", "GLOBALDATA_professions_athlete_givewoodandnails": "0", "GLOBALDATA_professions_athlete_repairarmour": "0", "GLOBALDATA_professions_athlete_cookfood": "0", "GLOBALDATA_professions_athlete_growfood": "0", "GLOBALDATA_professions_athlete_growdrugs": "0", "GLOBALDATA_professions_athlete_expdiff": "0", "GLOBALDATA_professions_athlete_hasweapon": "", "GLOBALDATA_professions_athlete_hasweaponname": "", "GLOBALDATA_professions_athlete_hasitem": "", "GLOBALDATA_professions_athlete_hasitemname": "", "GLOBALDATA_professions_athlete_bonuscash": "0", "GLOBALDATA_professions_athlete_shirt": "vest_colourWhite", "GLOBALDATA_professions_athlete_trousers": "jeans_colourBlue", "GLOBALDATA_professions_athlete_description": "<font color=\"#25db00\">+15% Movement Speed</font>", "GLOBALDATA_professions_athlete_details": "<font color=\"red\">Stat Details</font> <br />- Agility increases your base movement speed.", "GLOBALDATA_professions_musician_strength": "0", "GLOBALDATA_professions_musician_agility": "0", "GLOBALDATA_professions_musician_accuracy": "0", "GLOBALDATA_professions_musician_endurance": "0", "GLOBALDATA_professions_musician_criticalhit": "0", "GLOBALDATA_professions_musician_reloading": "0", "GLOBALDATA_professions_musician_dexterity": "30", "GLOBALDATA_professions_musician_survival": "0", "GLOBALDATA_professions_musician_evasion": "0", "GLOBALDATA_professions_musician_promelee": "0", "GLOBALDATA_professions_musician_propistol": "0", "GLOBALDATA_professions_musician_proshotgun": "0", "GLOBALDATA_professions_musician_promachinegun": "0", "GLOBALDATA_professions_musician_proexplosive": "0", "GLOBALDATA_professions_musician_prorifle": "0", "GLOBALDATA_professions_musician_givemedicene": "0", "GLOBALDATA_professions_musician_givewoodandnails": "0", "GLOBALDATA_professions_musician_repairarmour": "0", "GLOBALDATA_professions_musician_cookfood": "0", "GLOBALDATA_professions_musician_growfood": "0", "GLOBALDATA_professions_musician_growdrugs": "0", "GLOBALDATA_professions_musician_expdiff": "20", "GLOBALDATA_professions_musician_hasweapon": "", "GLOBALDATA_professions_musician_hasweaponname": "", "GLOBALDATA_professions_musician_hasitem": "", "GLOBALDATA_professions_musician_hasitemname": "", "GLOBALDATA_professions_musician_bonuscash": "0", "GLOBALDATA_professions_musician_shirt": "tshirt_<PERSON><PERSON><PERSON>", "GLOBALDATA_professions_musician_trousers": "jeans_colour<PERSON>rey", "GLOBALDATA_professions_musician_description": "<font color=\"#25db00\">+10% Melee Weapon Damage (Non-Chainsaw)</font>", "GLOBALDATA_professions_musician_details": "<font color=\"red\">Stat Details</font> <br />- Dexterity increases your attack speed and weapon maneuvering.", "GLOBALDATA_professions_scavenger_strength": "0", "GLOBALDATA_professions_scavenger_agility": "15", "GLOBALDATA_professions_scavenger_accuracy": "0", "GLOBALDATA_professions_scavenger_endurance": "0", "GLOBALDATA_professions_scavenger_criticalhit": "0", "GLOBALDATA_professions_scavenger_reloading": "0", "GLOBALDATA_professions_scavenger_dexterity": "0", "GLOBALDATA_professions_scavenger_survival": "15", "GLOBALDATA_professions_scavenger_evasion": "0", "GLOBALDATA_professions_scavenger_promelee": "0", "GLOBALDATA_professions_scavenger_propistol": "0", "GLOBALDATA_professions_scavenger_proshotgun": "0", "GLOBALDATA_professions_scavenger_promachinegun": "0", "GLOBALDATA_professions_scavenger_proexplosive": "0", "GLOBALDATA_professions_scavenger_prorifle": "0", "GLOBALDATA_professions_scavenger_givemedicene": "0", "GLOBALDATA_professions_scavenger_givewoodandnails": "0", "GLOBALDATA_professions_scavenger_repairarmour": "0", "GLOBALDATA_professions_scavenger_cookfood": "0", "GLOBALDATA_professions_scavenger_growfood": "0", "GLOBALDATA_professions_scavenger_growdrugs": "0", "GLOBALDATA_professions_scavenger_expdiff": "0", "GLOBALDATA_professions_scavenger_hasweapon": "", "GLOBALDATA_professions_scavenger_hasweaponname": "", "GLOBALDATA_professions_scavenger_hasitem": "", "GLOBALDATA_professions_scavenger_hasitemname": "", "GLOBALDATA_professions_scavenger_bonuscash": "0", "GLOBALDATA_professions_scavenger_shirt": "tshirt_colourWhite", "GLOBALDATA_professions_scavenger_trousers": "jeans_colourBrown", "GLOBALDATA_professions_scavenger_description": "<font color=\"#25db00\">+75% Ammunition Find Quantity<br />+15% Weapon Find Chance<br />+15% Armour Find Chance</font>", "GLOBALDATA_professions_scavenger_details": "<font color=\"red\">Stat Details</font> <br />- Survival decreases the distance infected can see or hear you, and increases melee weapon damage (non-Chainsaw). <br /><br /> <font color=\"red\">Boost Details</font> <br />Weapon and Armor Chance: This boost increases your odds of finding weapons or armor when searching an object. It is not a flat bonus but instead multiplies the base weapon and armor chance of that area", "GLOBALDATA_professions_teacher_strength": "0", "GLOBALDATA_professions_teacher_agility": "0", "GLOBALDATA_professions_teacher_accuracy": "0", "GLOBALDATA_professions_teacher_endurance": "0", "GLOBALDATA_professions_teacher_criticalhit": "0", "GLOBALDATA_professions_teacher_reloading": "0", "GLOBALDATA_professions_teacher_dexterity": "0", "GLOBALDATA_professions_teacher_survival": "0", "GLOBALDATA_professions_teacher_evasion": "0", "GLOBALDATA_professions_teacher_promelee": "0", "GLOBALDATA_professions_teacher_propistol": "0", "GLOBALDATA_professions_teacher_proshotgun": "0", "GLOBALDATA_professions_teacher_promachinegun": "0", "GLOBALDATA_professions_teacher_proexplosive": "0", "GLOBALDATA_professions_teacher_prorifle": "0", "GLOBALDATA_professions_teacher_givemedicene": "0", "GLOBALDATA_professions_teacher_givewoodandnails": "0", "GLOBALDATA_professions_teacher_repairarmour": "0", "GLOBALDATA_professions_teacher_cookfood": "0", "GLOBALDATA_professions_teacher_growfood": "0", "GLOBALDATA_professions_teacher_growdrugs": "0", "GLOBALDATA_professions_teacher_expdiff": "30", "GLOBALDATA_professions_teacher_hasweapon": "", "GLOBALDATA_professions_teacher_hasweaponname": "", "GLOBALDATA_professions_teacher_hasitem": "", "GLOBALDATA_professions_teacher_hasitemname": "", "GLOBALDATA_professions_teacher_bonuscash": "0", "GLOBALDATA_professions_teacher_shirt": "shirt_colour<PERSON>hite", "GLOBALDATA_professions_teacher_trousers": "trousers_colour<PERSON>rey", "GLOBALDATA_professions_teacher_description": "", "GLOBALDATA_professions_teacher_details": "", "GLOBALDATA_professions_priest_strength": "0", "GLOBALDATA_professions_priest_agility": "0", "GLOBALDATA_professions_priest_accuracy": "0", "GLOBALDATA_professions_priest_endurance": "0", "GLOBALDATA_professions_priest_criticalhit": "0", "GLOBALDATA_professions_priest_reloading": "0", "GLOBALDATA_professions_priest_dexterity": "0", "GLOBALDATA_professions_priest_survival": "0", "GLOBALDATA_professions_priest_evasion": "0", "GLOBALDATA_professions_priest_promelee": "0", "GLOBALDATA_professions_priest_propistol": "0", "GLOBALDATA_professions_priest_proshotgun": "0", "GLOBALDATA_professions_priest_promachinegun": "0", "GLOBALDATA_professions_priest_proexplosive": "0", "GLOBALDATA_professions_priest_prorifle": "0", "GLOBALDATA_professions_priest_givemedicene": "0", "GLOBALDATA_professions_priest_givewoodandnails": "0", "GLOBALDATA_professions_priest_repairarmour": "0", "GLOBALDATA_professions_priest_cookfood": "0", "GLOBALDATA_professions_priest_growfood": "0", "GLOBALDATA_professions_priest_growdrugs": "0", "GLOBALDATA_professions_priest_expdiff": "30", "GLOBALDATA_professions_priest_hasweapon": "", "GLOBALDATA_professions_priest_hasweaponname": "", "GLOBALDATA_professions_priest_hasitem": "", "GLOBALDATA_professions_priest_hasitemname": "", "GLOBALDATA_professions_priest_bonuscash": "0", "GLOBALDATA_professions_priest_shirt": "shirt_colourBlack", "GLOBALDATA_professions_priest_trousers": "trousers_colourBlack", "GLOBALDATA_professions_priest_description": "", "GLOBALDATA_professions_priest_details": "", "GLOBALDATA_professions_lawyer_strength": "0", "GLOBALDATA_professions_lawyer_agility": "0", "GLOBALDATA_professions_lawyer_accuracy": "0", "GLOBALDATA_professions_lawyer_endurance": "0", "GLOBALDATA_professions_lawyer_criticalhit": "0", "GLOBALDATA_professions_lawyer_reloading": "0", "GLOBALDATA_professions_lawyer_dexterity": "0", "GLOBALDATA_professions_lawyer_survival": "0", "GLOBALDATA_professions_lawyer_evasion": "0", "GLOBALDATA_professions_lawyer_promelee": "0", "GLOBALDATA_professions_lawyer_propistol": "0", "GLOBALDATA_professions_lawyer_proshotgun": "0", "GLOBALDATA_professions_lawyer_promachinegun": "0", "GLOBALDATA_professions_lawyer_proexplosive": "0", "GLOBALDATA_professions_lawyer_prorifle": "0", "GLOBALDATA_professions_lawyer_givemedicene": "0", "GLOBALDATA_professions_lawyer_givewoodandnails": "0", "GLOBALDATA_professions_lawyer_repairarmour": "0", "GLOBALDATA_professions_lawyer_cookfood": "0", "GLOBALDATA_professions_lawyer_growfood": "0", "GLOBALDATA_professions_lawyer_growdrugs": "0", "GLOBALDATA_professions_lawyer_expdiff": "30", "GLOBALDATA_professions_lawyer_hasweapon": "", "GLOBALDATA_professions_lawyer_hasweaponname": "", "GLOBALDATA_professions_lawyer_hasitem": "", "GLOBALDATA_professions_lawyer_hasitemname": "", "GLOBALDATA_professions_lawyer_bonuscash": "100", "GLOBALDATA_professions_lawyer_shirt": "shirt_colourBlack", "GLOBALDATA_professions_lawyer_trousers": "trousers_colourBlack", "GLOBALDATA_professions_lawyer_description": "", "GLOBALDATA_professions_lawyer_details": "", "GLOBALDATA_professions_accountant_strength": "0", "GLOBALDATA_professions_accountant_agility": "0", "GLOBALDATA_professions_accountant_accuracy": "0", "GLOBALDATA_professions_accountant_endurance": "0", "GLOBALDATA_professions_accountant_criticalhit": "0", "GLOBALDATA_professions_accountant_reloading": "0", "GLOBALDATA_professions_accountant_dexterity": "0", "GLOBALDATA_professions_accountant_survival": "0", "GLOBALDATA_professions_accountant_evasion": "0", "GLOBALDATA_professions_accountant_promelee": "0", "GLOBALDATA_professions_accountant_propistol": "0", "GLOBALDATA_professions_accountant_proshotgun": "0", "GLOBALDATA_professions_accountant_promachinegun": "0", "GLOBALDATA_professions_accountant_proexplosive": "0", "GLOBALDATA_professions_accountant_prorifle": "0", "GLOBALDATA_professions_accountant_givemedicene": "0", "GLOBALDATA_professions_accountant_givewoodandnails": "0", "GLOBALDATA_professions_accountant_repairarmour": "0", "GLOBALDATA_professions_accountant_cookfood": "0", "GLOBALDATA_professions_accountant_growfood": "0", "GLOBALDATA_professions_accountant_growdrugs": "0", "GLOBALDATA_professions_accountant_expdiff": "30", "GLOBALDATA_professions_accountant_hasweapon": "", "GLOBALDATA_professions_accountant_hasweaponname": "", "GLOBALDATA_professions_accountant_hasitem": "", "GLOBALDATA_professions_accountant_hasitemname": "", "GLOBALDATA_professions_accountant_bonuscash": "50", "GLOBALDATA_professions_accountant_shirt": "shirt_colour<PERSON>hite", "GLOBALDATA_professions_accountant_trousers": "trousers_colour<PERSON>rey", "GLOBALDATA_professions_accountant_description": "", "GLOBALDATA_professions_accountant_details": "", "GLOBALDATA_professions_journalist_strength": "0", "GLOBALDATA_professions_journalist_agility": "0", "GLOBALDATA_professions_journalist_accuracy": "0", "GLOBALDATA_professions_journalist_endurance": "0", "GLOBALDATA_professions_journalist_criticalhit": "0", "GLOBALDATA_professions_journalist_reloading": "0", "GLOBALDATA_professions_journalist_dexterity": "0", "GLOBALDATA_professions_journalist_survival": "0", "GLOBALDATA_professions_journalist_evasion": "0", "GLOBALDATA_professions_journalist_promelee": "0", "GLOBALDATA_professions_journalist_propistol": "0", "GLOBALDATA_professions_journalist_proshotgun": "0", "GLOBALDATA_professions_journalist_promachinegun": "0", "GLOBALDATA_professions_journalist_proexplosive": "0", "GLOBALDATA_professions_journalist_prorifle": "0", "GLOBALDATA_professions_journalist_givemedicene": "0", "GLOBALDATA_professions_journalist_givewoodandnails": "0", "GLOBALDATA_professions_journalist_repairarmour": "0", "GLOBALDATA_professions_journalist_cookfood": "0", "GLOBALDATA_professions_journalist_growfood": "0", "GLOBALDATA_professions_journalist_growdrugs": "0", "GLOBALDATA_professions_journalist_expdiff": "30", "GLOBALDATA_professions_journalist_hasweapon": "", "GLOBALDATA_professions_journalist_hasweaponname": "", "GLOBALDATA_professions_journalist_hasitem": "", "GLOBALDATA_professions_journalist_hasitemname": "", "GLOBALDATA_professions_journalist_bonuscash": "50", "GLOBALDATA_professions_journalist_shirt": "shirt_colour<PERSON>hite", "GLOBALDATA_professions_journalist_trousers": "trousers_colour<PERSON>rey", "GLOBALDATA_professions_journalist_description": "", "GLOBALDATA_professions_journalist_details": "", "GLOBALDATA_professions_actor_strength": "0", "GLOBALDATA_professions_actor_agility": "0", "GLOBALDATA_professions_actor_accuracy": "0", "GLOBALDATA_professions_actor_endurance": "0", "GLOBALDATA_professions_actor_criticalhit": "0", "GLOBALDATA_professions_actor_reloading": "0", "GLOBALDATA_professions_actor_dexterity": "0", "GLOBALDATA_professions_actor_survival": "0", "GLOBALDATA_professions_actor_evasion": "0", "GLOBALDATA_professions_actor_promelee": "0", "GLOBALDATA_professions_actor_propistol": "0", "GLOBALDATA_professions_actor_proshotgun": "0", "GLOBALDATA_professions_actor_promachinegun": "0", "GLOBALDATA_professions_actor_proexplosive": "0", "GLOBALDATA_professions_actor_prorifle": "0", "GLOBALDATA_professions_actor_givemedicene": "0", "GLOBALDATA_professions_actor_givewoodandnails": "0", "GLOBALDATA_professions_actor_repairarmour": "0", "GLOBALDATA_professions_actor_cookfood": "0", "GLOBALDATA_professions_actor_growfood": "0", "GLOBALDATA_professions_actor_growdrugs": "0", "GLOBALDATA_professions_actor_expdiff": "30", "GLOBALDATA_professions_actor_hasweapon": "", "GLOBALDATA_professions_actor_hasweaponname": "", "GLOBALDATA_professions_actor_hasitem": "", "GLOBALDATA_professions_actor_hasitemname": "", "GLOBALDATA_professions_actor_bonuscash": "20", "GLOBALDATA_professions_actor_shirt": "tshirt_colourWhite", "GLOBALDATA_professions_actor_trousers": "jeans_colourBlue", "GLOBALDATA_professions_actor_description": "", "GLOBALDATA_professions_actor_details": "", "GLOBALDATA_professions_stock broker_strength": "0", "GLOBALDATA_professions_stock broker_agility": "0", "GLOBALDATA_professions_stock broker_accuracy": "0", "GLOBALDATA_professions_stock broker_endurance": "0", "GLOBALDATA_professions_stock broker_criticalhit": "0", "GLOBALDATA_professions_stock broker_reloading": "0", "GLOBALDATA_professions_stock broker_dexterity": "0", "GLOBALDATA_professions_stock broker_survival": "0", "GLOBALDATA_professions_stock broker_evasion": "0", "GLOBALDATA_professions_stock broker_promelee": "0", "GLOBALDATA_professions_stock broker_propistol": "0", "GLOBALDATA_professions_stock broker_proshotgun": "0", "GLOBALDATA_professions_stock broker_promachinegun": "0", "GLOBALDATA_professions_stock broker_proexplosive": "0", "GLOBALDATA_professions_stock broker_prorifle": "0", "GLOBALDATA_professions_stock broker_givemedicene": "0", "GLOBALDATA_professions_stock broker_givewoodandnails": "0", "GLOBALDATA_professions_stock broker_repairarmour": "0", "GLOBALDATA_professions_stock broker_cookfood": "0", "GLOBALDATA_professions_stock broker_growfood": "0", "GLOBALDATA_professions_stock broker_growdrugs": "0", "GLOBALDATA_professions_stock broker_expdiff": "30", "GLOBALDATA_professions_stock broker_hasweapon": "", "GLOBALDATA_professions_stock broker_hasweaponname": "", "GLOBALDATA_professions_stock broker_hasitem": "", "GLOBALDATA_professions_stock broker_hasitemname": "", "GLOBALDATA_professions_stock broker_bonuscash": "100", "GLOBALDATA_professions_stock broker_shirt": "shirt_colour<PERSON>hite", "GLOBALDATA_professions_stock broker_trousers": "trousers_colour<PERSON>rey", "GLOBALDATA_professions_stock broker_description": "", "GLOBALDATA_professions_stock broker_details": "", "GLOBALDATA_professions_architect_strength": "0", "GLOBALDATA_professions_architect_agility": "0", "GLOBALDATA_professions_architect_accuracy": "0", "GLOBALDATA_professions_architect_endurance": "0", "GLOBALDATA_professions_architect_criticalhit": "0", "GLOBALDATA_professions_architect_reloading": "0", "GLOBALDATA_professions_architect_dexterity": "0", "GLOBALDATA_professions_architect_survival": "0", "GLOBALDATA_professions_architect_evasion": "0", "GLOBALDATA_professions_architect_promelee": "0", "GLOBALDATA_professions_architect_propistol": "0", "GLOBALDATA_professions_architect_proshotgun": "0", "GLOBALDATA_professions_architect_promachinegun": "0", "GLOBALDATA_professions_architect_proexplosive": "0", "GLOBALDATA_professions_architect_prorifle": "0", "GLOBALDATA_professions_architect_givemedicene": "0", "GLOBALDATA_professions_architect_givewoodandnails": "0", "GLOBALDATA_professions_architect_repairarmour": "0", "GLOBALDATA_professions_architect_cookfood": "0", "GLOBALDATA_professions_architect_growfood": "0", "GLOBALDATA_professions_architect_growdrugs": "0", "GLOBALDATA_professions_architect_expdiff": "30", "GLOBALDATA_professions_architect_hasweapon": "", "GLOBALDATA_professions_architect_hasweaponname": "", "GLOBALDATA_professions_architect_hasitem": "", "GLOBALDATA_professions_architect_hasitemname": "", "GLOBALDATA_professions_architect_bonuscash": "50", "GLOBALDATA_professions_architect_shirt": "shirt_colour<PERSON>hite", "GLOBALDATA_professions_architect_trousers": "trousers_colour<PERSON>rey", "GLOBALDATA_professions_architect_description": "", "GLOBALDATA_professions_architect_details": "", "GLOBALDATA_professions_entertainer_strength": "0", "GLOBALDATA_professions_entertainer_agility": "0", "GLOBALDATA_professions_entertainer_accuracy": "0", "GLOBALDATA_professions_entertainer_endurance": "0", "GLOBALDATA_professions_entertainer_criticalhit": "0", "GLOBALDATA_professions_entertainer_reloading": "0", "GLOBALDATA_professions_entertainer_dexterity": "0", "GLOBALDATA_professions_entertainer_survival": "0", "GLOBALDATA_professions_entertainer_evasion": "0", "GLOBALDATA_professions_entertainer_promelee": "0", "GLOBALDATA_professions_entertainer_propistol": "0", "GLOBALDATA_professions_entertainer_proshotgun": "0", "GLOBALDATA_professions_entertainer_promachinegun": "0", "GLOBALDATA_professions_entertainer_proexplosive": "0", "GLOBALDATA_professions_entertainer_prorifle": "0", "GLOBALDATA_professions_entertainer_givemedicene": "0", "GLOBALDATA_professions_entertainer_givewoodandnails": "0", "GLOBALDATA_professions_entertainer_repairarmour": "0", "GLOBALDATA_professions_entertainer_cookfood": "0", "GLOBALDATA_professions_entertainer_growfood": "0", "GLOBALDATA_professions_entertainer_growdrugs": "0", "GLOBALDATA_professions_entertainer_expdiff": "30", "GLOBALDATA_professions_entertainer_hasweapon": "", "GLOBALDATA_professions_entertainer_hasweaponname": "", "GLOBALDATA_professions_entertainer_hasitem": "", "GLOBALDATA_professions_entertainer_hasitemname": "", "GLOBALDATA_professions_entertainer_bonuscash": "20", "GLOBALDATA_professions_entertainer_shirt": "shirt_colourBlack", "GLOBALDATA_professions_entertainer_trousers": "jeans_colourBlue", "GLOBALDATA_professions_entertainer_description": "", "GLOBALDATA_professions_entertainer_details": "", "GLOBALDATA_professions_artist_strength": "0", "GLOBALDATA_professions_artist_agility": "0", "GLOBALDATA_professions_artist_accuracy": "0", "GLOBALDATA_professions_artist_endurance": "0", "GLOBALDATA_professions_artist_criticalhit": "0", "GLOBALDATA_professions_artist_reloading": "0", "GLOBALDATA_professions_artist_dexterity": "0", "GLOBALDATA_professions_artist_survival": "0", "GLOBALDATA_professions_artist_evasion": "0", "GLOBALDATA_professions_artist_promelee": "0", "GLOBALDATA_professions_artist_propistol": "0", "GLOBALDATA_professions_artist_proshotgun": "0", "GLOBALDATA_professions_artist_promachinegun": "0", "GLOBALDATA_professions_artist_proexplosive": "0", "GLOBALDATA_professions_artist_prorifle": "0", "GLOBALDATA_professions_artist_givemedicene": "0", "GLOBALDATA_professions_artist_givewoodandnails": "0", "GLOBALDATA_professions_artist_repairarmour": "0", "GLOBALDATA_professions_artist_cookfood": "0", "GLOBALDATA_professions_artist_growfood": "0", "GLOBALDATA_professions_artist_growdrugs": "0", "GLOBALDATA_professions_artist_expdiff": "30", "GLOBALDATA_professions_artist_hasweapon": "", "GLOBALDATA_professions_artist_hasweaponname": "", "GLOBALDATA_professions_artist_hasitem": "", "GLOBALDATA_professions_artist_hasitemname": "", "GLOBALDATA_professions_artist_bonuscash": "20", "GLOBALDATA_professions_artist_shirt": "vest_colourWhite", "GLOBALDATA_professions_artist_trousers": "jeans_colour<PERSON><PERSON><PERSON>", "GLOBALDATA_professions_artist_description": "", "GLOBALDATA_professions_artist_details": "", "GLOBALDATA_professions_student_strength": "5", "GLOBALDATA_professions_student_agility": "5", "GLOBALDATA_professions_student_accuracy": "0", "GLOBALDATA_professions_student_endurance": "5", "GLOBALDATA_professions_student_criticalhit": "0", "GLOBALDATA_professions_student_reloading": "0", "GLOBALDATA_professions_student_dexterity": "0", "GLOBALDATA_professions_student_survival": "0", "GLOBALDATA_professions_student_evasion": "0", "GLOBALDATA_professions_student_promelee": "0", "GLOBALDATA_professions_student_propistol": "0", "GLOBALDATA_professions_student_proshotgun": "0", "GLOBALDATA_professions_student_promachinegun": "0", "GLOBALDATA_professions_student_proexplosive": "0", "GLOBALDATA_professions_student_prorifle": "0", "GLOBALDATA_professions_student_givemedicene": "0", "GLOBALDATA_professions_student_givewoodandnails": "0", "GLOBALDATA_professions_student_repairarmour": "0", "GLOBALDATA_professions_student_cookfood": "0", "GLOBALDATA_professions_student_growfood": "0", "GLOBALDATA_professions_student_growdrugs": "0", "GLOBALDATA_professions_student_expdiff": "25", "GLOBALDATA_professions_student_hasweapon": "", "GLOBALDATA_professions_student_hasweaponname": "", "GLOBALDATA_professions_student_hasitem": "", "GLOBALDATA_professions_student_hasitemname": "", "GLOBALDATA_professions_student_bonuscash": "0", "GLOBALDATA_professions_student_shirt": "tshirt_colourWhite", "GLOBALDATA_professions_student_trousers": "jeans_colourBlue", "GLOBALDATA_professions_student_description": "", "GLOBALDATA_professions_student_details": "", "GLOBALDATA_professions_ironman_strength": "0", "GLOBALDATA_professions_ironman_agility": "0", "GLOBALDATA_professions_ironman_accuracy": "0", "GLOBALDATA_professions_ironman_endurance": "0", "GLOBALDATA_professions_ironman_criticalhit": "0", "GLOBALDATA_professions_ironman_reloading": "0", "GLOBALDATA_professions_ironman_dexterity": "0", "GLOBALDATA_professions_ironman_survival": "0", "GLOBALDATA_professions_ironman_evasion": "0", "GLOBALDATA_professions_ironman_promelee": "0", "GLOBALDATA_professions_ironman_propistol": "0", "GLOBALDATA_professions_ironman_proshotgun": "0", "GLOBALDATA_professions_ironman_promachinegun": "0", "GLOBALDATA_professions_ironman_proexplosive": "0", "GLOBALDATA_professions_ironman_prorifle": "0", "GLOBALDATA_professions_ironman_givemedicene": "1", "GLOBALDATA_professions_ironman_givewoodandnails": "0", "GLOBALDATA_professions_ironman_repairarmour": "1", "GLOBALDATA_professions_ironman_cookfood": "1", "GLOBALDATA_professions_ironman_growfood": "0", "GLOBALDATA_professions_ironman_growdrugs": "0", "GLOBALDATA_professions_ironman_expdiff": "0", "GLOBALDATA_professions_ironman_hasweapon": "", "GLOBALDATA_professions_ironman_hasweaponname": "", "GLOBALDATA_professions_ironman_hasitem": "", "GLOBALDATA_professions_ironman_hasitemname": "", "GLOBALDATA_professions_ironman_bonuscash": "0", "GLOBALDATA_professions_ironman_shirt": "shirt_colourBlack", "GLOBALDATA_professions_ironman_trousers": "trousers_colourBrown", "GLOBALDATA_professions_ironman_description": "<span style=\"color: red;\">Ironman accounts cannot trade! Not for regular players.</span>", "GLOBALDATA_professions_ironman_details": "", "GLOBALDATA_professions_hardcore_strength": "0", "GLOBALDATA_professions_hardcore_agility": "0", "GLOBALDATA_professions_hardcore_accuracy": "0", "GLOBALDATA_professions_hardcore_endurance": "0", "GLOBALDATA_professions_hardcore_criticalhit": "0", "GLOBALDATA_professions_hardcore_reloading": "0", "GLOBALDATA_professions_hardcore_dexterity": "0", "GLOBALDATA_professions_hardcore_survival": "0", "GLOBALDATA_professions_hardcore_evasion": "0", "GLOBALDATA_professions_hardcore_promelee": "0", "GLOBALDATA_professions_hardcore_propistol": "0", "GLOBALDATA_professions_hardcore_proshotgun": "0", "GLOBALDATA_professions_hardcore_promachinegun": "0", "GLOBALDATA_professions_hardcore_proexplosive": "0", "GLOBALDATA_professions_hardcore_prorifle": "0", "GLOBALDATA_professions_hardcore_givemedicene": "1", "GLOBALDATA_professions_hardcore_givewoodandnails": "0", "GLOBALDATA_professions_hardcore_repairarmour": "1", "GLOBALDATA_professions_hardcore_cookfood": "1", "GLOBALDATA_professions_hardcore_growfood": "0", "GLOBALDATA_professions_hardcore_growdrugs": "0", "GLOBALDATA_professions_hardcore_expdiff": "30", "GLOBALDATA_professions_hardcore_hasweapon": "", "GLOBALDATA_professions_hardcore_hasweaponname": "", "GLOBALDATA_professions_hardcore_hasitem": "", "GLOBALDATA_professions_hardcore_hasitemname": "", "GLOBALDATA_professions_hardcore_bonuscash": "0", "GLOBALDATA_professions_hardcore_shirt": "shirt_colourBlack", "GLOBALDATA_professions_hardcore_trousers": "trousers_colourBrown", "GLOBALDATA_professions_hardcore_description": "<span style=\"color: red;\">Hardcore accounts are perma-death.</span>", "GLOBALDATA_professions_hardcore_details": "", "GLOBALDATA_professions_hardcore ironman_strength": "0", "GLOBALDATA_professions_hardcore ironman_agility": "0", "GLOBALDATA_professions_hardcore ironman_accuracy": "0", "GLOBALDATA_professions_hardcore ironman_endurance": "0", "GLOBALDATA_professions_hardcore ironman_criticalhit": "0", "GLOBALDATA_professions_hardcore ironman_reloading": "0", "GLOBALDATA_professions_hardcore ironman_dexterity": "0", "GLOBALDATA_professions_hardcore ironman_survival": "0", "GLOBALDATA_professions_hardcore ironman_evasion": "0", "GLOBALDATA_professions_hardcore ironman_promelee": "0", "GLOBALDATA_professions_hardcore ironman_propistol": "0", "GLOBALDATA_professions_hardcore ironman_proshotgun": "0", "GLOBALDATA_professions_hardcore ironman_promachinegun": "0", "GLOBALDATA_professions_hardcore ironman_proexplosive": "0", "GLOBALDATA_professions_hardcore ironman_prorifle": "0", "GLOBALDATA_professions_hardcore ironman_givemedicene": "1", "GLOBALDATA_professions_hardcore ironman_givewoodandnails": "0", "GLOBALDATA_professions_hardcore ironman_repairarmour": "1", "GLOBALDATA_professions_hardcore ironman_cookfood": "1", "GLOBALDATA_professions_hardcore ironman_growfood": "0", "GLOBALDATA_professions_hardcore ironman_growdrugs": "0", "GLOBALDATA_professions_hardcore ironman_expdiff": "0", "GLOBALDATA_professions_hardcore ironman_hasweapon": "", "GLOBALDATA_professions_hardcore ironman_hasweaponname": "", "GLOBALDATA_professions_hardcore ironman_hasitem": "", "GLOBALDATA_professions_hardcore ironman_hasitemname": "", "GLOBALDATA_professions_hardcore ironman_bonuscash": "0", "GLOBALDATA_professions_hardcore ironman_shirt": "shirt_colourBlack", "GLOBALDATA_professions_hardcore ironman_trousers": "trousers_colourBrown", "GLOBALDATA_professions_hardcore ironman_description": "<span style=\"color: red;\">Hardcore accounts are perma-death.</span><br /><span style=\"color: red;\">Ironman accounts cannot trade! Not for regular players.</span>", "GLOBALDATA_professions_hardcore ironman_details": "", "GLOBALDATA_professionlist1": "Doctor", "GLOBALDATA_professionlist2": "Chef", "GLOBALDATA_professionlist3": "Engineer", "GLOBALDATA_professionlist4": "Scientist", "GLOBALDATA_professionlist5": "<PERSON>", "GLOBALDATA_professionlist6": "Craftsman", "GLOBALDATA_professionlist7": "Weaponsmith", "GLOBALDATA_professionlist8": "Fireman", "GLOBALDATA_professionlist9": "Boxer", "GLOBALDATA_professionlist10": "Landscaper", "GLOBALDATA_professionlist11": "Lumber<PERSON>", "GLOBALDATA_professionlist12": "Investigator", "GLOBALDATA_professionlist13": "Police Officer", "GLOBALDATA_professionlist14": "Guard", "GLOBALDATA_professionlist15": "<PERSON>", "GLOBALDATA_professionlist16": "Survivalist", "GLOBALDATA_professionlist17": "SWAT", "GLOBALDATA_professionlist18": "Soldier", "GLOBALDATA_professionlist19": "Grenadier", "GLOBALDATA_professionlist20": "Criminal", "GLOBALDATA_professionlist21": "<PERSON><PERSON><PERSON>", "GLOBALDATA_professionlist22": "Athlete", "GLOBALDATA_professionlist23": "<PERSON>ian", "GLOBALDATA_professionlist24": "Teacher", "GLOBALDATA_professionlist25": "Priest", "GLOBALDATA_professionlist26": "Lawyer", "GLOBALDATA_professionlist27": "Accountant", "GLOBALDATA_professionlist28": "Journalist", "GLOBALDATA_professionlist29": "Actor", "GLOBALDATA_professionlist30": "Stock Broker", "GLOBALDATA_professionlist31": "Architect", "GLOBALDATA_professionlist32": "Entertainer", "GLOBALDATA_professionlist33": "Artist", "GLOBALDATA_professionlist34": "Student", "GLOBALDATA_professions_maxpros": "34", "pagetime": "**********", "imgver": "97"}