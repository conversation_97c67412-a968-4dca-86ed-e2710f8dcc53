/**
 * for web calls, see updateInventory
 */

declare global {
  // reloads inventory data from server, then update UI
  function reloadInventoryData(): void;
  function reloadStorageData(invData: string): void;

  function populateInventory(): void;
}

/**
 * can be called from inventory
 */
function loadStorage() {
  let dataArr = {
    pagetime: userVars["pagetime"],
    sc: userVars["sc"],
    userID: userVars["userID"],
    password: userVars["password"],
  };

  webCall(
    "get_storage",
    dataArr,
    function (data) {
      unsafeWindow.storageBox = flshToArr(data);
    },
    true
  );
}

function moveAllToStorage() {
  let dataArr = {
    pagetime: userVars["pagetime"],
    templateID: userVars["template_ID"],
    sc: userVars["sc"],
    gv: 21,
    userID: userVars["userID"],
    password: userVars["password"],
    action: "tostorage",
    slotnum: 1,
  };
  playSound("swap");
  promptLoading();
  webCall(
    "hotrods/inventory_actions",
    dataArr,
    (data) => {
      promptEnd();
      updateIntoArr(flshToArr(data, "DFSTATS_") as any, userVars);
      populateInventory();
    },
    true
  );
  reloadInventoryData();
}

function moveAllFromStorage() {
  let dataArr = {
    pagetime: userVars["pagetime"],
    templateID: userVars["template_ID"],
    sc: userVars["sc"],
    gv: 21,
    userID: userVars["userID"],
    password: userVars["password"],
    action: "fromstorage",
    slotnum: 1,
  };
  playSound("swap");
  promptLoading();
  webCall(
    "hotrods/inventory_actions",
    dataArr,
    (data) => {
      promptEnd();
      updateIntoArr(flshToArr(data, "DFSTATS_") as any, userVars);
      populateInventory();
    },
    true
  );
  reloadInventoryData();
}

function uiAddMoveStorageButtons() {
  const html = `<div style="position: absolute; right: 13px; bottom: 86px; z-index: 1;"><button id="storagetoinv" data-pmoverride=""><img src="/onlinezombiemmo/hotrods/hotrods_v${hrV}/HTML5/images/movein.png" width="40" data-amchild=""></button><button id="invtostorage" data-pmoverride=""><img src="/onlinezombiemmo/hotrods/hotrods_v${hrV}/HTML5/images/moveout.png" width="40" data-amchild=""></button></div>`;

  document
    .getElementById("inventoryholder")
    .insertAdjacentHTML("beforeend", html);

  document.getElementById("storagetoinv").addEventListener("click", () => {
    moveAllFromStorage();
  });

  document.getElementById("invtostorage").addEventListener("click", () => {
    moveAllToStorage();
  });

  // move up discard button
  const discardButton = document.querySelector<HTMLElement>(
    'div.fakeSlot.hoverEffect[data-action="discard"]'
  );
  discardButton.style.bottom = "150px";
  const discardLabel = [
    ...document.querySelectorAll<HTMLElement>("div.opElem"),
  ].find((el) => el.textContent.trim() === "Discard");
  discardLabel.style.bottom = "134px";
}

export default {
  loadStorage,
  moveAllToStorage,
  moveAllFromStorage,
  uiAddMoveStorageButtons,
};
