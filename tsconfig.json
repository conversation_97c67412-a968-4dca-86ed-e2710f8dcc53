{
  "compilerOptions": {
    "rootDir": "./src",

    "module": "none",
    "target": "esnext",

    // Other Outputs
    "sourceMap": false,
    "declaration": false,
    "declarationMap": false,
    "removeComments": true,

    // Stricter Typechecking Options
    "noUncheckedIndexedAccess": true,

    // Style Options
    "noImplicitReturns": true,
    "noImplicitOverride": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noPropertyAccessFromIndexSignature": true,

    // Recommended Options
    "strict": false,
    "jsx": "react-jsx",
    "noUncheckedSideEffectImports": true,
    "skipLibCheck": true,

    // Module Resolution
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true
  }
}
